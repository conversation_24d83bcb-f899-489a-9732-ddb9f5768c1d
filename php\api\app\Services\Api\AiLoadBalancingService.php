<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Models\AiModelConfig;
use App\Models\PlatformPerformanceMetric;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AiLoadBalancingService extends Service
{
    protected AiPlatformHealthService $healthService;
    protected AiPlatformSelectionService $selectionService;

    public function __construct(
        AiPlatformHealthService $healthService,
        AiPlatformSelectionService $selectionService
    ) {
        $this->healthService = $healthService;
        $this->selectionService = $selectionService;
    }

    /**
     * 分配批量任务到多个平台
     */
    public function distributeTasks(
        array $tasks,
        string $businessType,
        int $userId,
        array $config = []
    ): array {
        try {
            Log::info('开始AI负载均衡分配', [
                'task_count' => count($tasks),
                'business_type' => $businessType,
                'user_id' => $userId,
                'config' => $config
            ]);

            // 🔧 新增：验证业务类型
            if (!$this->selectionService->validateBusinessType($businessType)) {
                return [
                    'code' => 'INVALID_BUSINESS_TYPE',
                    'message' => "不支持的业务类型: {$businessType}",
                    'distribution' => [],
                    'supported_types' => array_keys($this->selectionService->getSupportedPlatforms($businessType))
                ];
            }

            // 获取可用平台
            $availablePlatforms = $this->getAvailablePlatforms($businessType);
            
            if (empty($availablePlatforms)) {
                return [
                    'code' => 'NO_AVAILABLE_PLATFORMS',
                    'message' => '没有可用的平台',
                    'distribution' => [],
                    'expected_platforms' => $this->selectionService->getSupportedPlatforms($businessType)
                ];
            }

            // 🔧 新增：验证任务数量限制
            $maxTasks = $config['max_tasks'] ?? 100;
            if (count($tasks) > $maxTasks) {
                return [
                    'code' => 'TOO_MANY_TASKS',
                    'message' => "任务数量超过限制，最大支持 {$maxTasks} 个任务",
                    'distribution' => [],
                    'task_count' => count($tasks),
                    'max_allowed' => $maxTasks
                ];
            }

            // 计算平台负载权重
            $platformWeights = $this->calculatePlatformWeights($availablePlatforms, $businessType, $config);
            
            // 分配任务
            $distribution = $this->allocateTasks($tasks, $platformWeights, $config);

            // 🔧 新增：验证分配结果
            $validationResult = $this->validateDistribution($distribution, $tasks);
            if (!$validationResult['valid']) {
                Log::error('负载均衡分配验证失败', $validationResult);
                return [
                    'code' => 'DISTRIBUTION_VALIDATION_FAILED',
                    'message' => '任务分配验证失败: ' . $validationResult['error'],
                    'distribution' => []
                ];
            }

            // 记录分配结果
            $this->recordDistribution($distribution, $businessType, $userId);

            return [
                'code' => 'SUCCESS',
                'message' => 'AI任务分配成功',
                'distribution' => $distribution,
                'summary' => [
                    'total_tasks' => count($tasks),
                    'platforms_used' => count($distribution),
                    'load_balancing_strategy' => $config['strategy'] ?? 'performance_based',
                    'estimated_completion' => $this->calculateOverallCompletionTime($distribution)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('AI负载均衡分配失败', [
                'business_type' => $businessType,
                'user_id' => $userId,
                'task_count' => count($tasks),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => 'DISTRIBUTION_FAILED',
                'message' => 'AI任务分配失败: ' . $e->getMessage(),
                'distribution' => []
            ];
        }
    }

    /**
     * 获取可用平台
     */
    protected function getAvailablePlatforms(string $businessType): array
    {
        $supportedPlatforms = $this->selectionService->getSupportedPlatforms($businessType);
        $availablePlatforms = [];

        foreach ($supportedPlatforms as $platform) {
            $models = AiModelConfig::where('platform', $platform)
                ->where('model_type', $businessType)
                ->where('is_active', true)
                ->where('health_status', 'healthy')
                ->get();

            if ($models->isNotEmpty()) {
                $availablePlatforms[] = [
                    'platform' => $platform,
                    'model_count' => $models->count(),
                    'models' => $models->toArray(),
                    'priority' => array_search($platform, $supportedPlatforms) + 1
                ];
            }
        }

        return $availablePlatforms;
    }

    /**
     * 计算平台负载权重
     */
    protected function calculatePlatformWeights(array $platforms, string $businessType, array $config): array
    {
        $weights = [];
        $strategy = $config['strategy'] ?? 'performance_based';
        
        foreach ($platforms as $platformData) {
            $platform = $platformData['platform'];
            
            // 获取平台性能数据
            $performance = $this->healthService->getPlatformAvailability($platform);
            
            // 获取当前负载
            $currentLoad = $this->getCurrentPlatformLoad($platform);
            
            // 🔧 修正：根据策略计算权重
            $weight = $this->calculateWeightByStrategy($strategy, $platformData, $performance, $currentLoad, $config);
            
            $weights[$platform] = [
                'weight' => max($weight, 0.1), // 最小权重0.1
                'current_load' => $currentLoad,
                'performance' => $performance,
                'models' => $platformData['models'],
                'priority' => $platformData['priority'],
                'strategy' => $strategy
            ];
        }

        return $weights;
    }

    /**
     * 🔧 新增：根据策略计算权重
     */
    protected function calculateWeightByStrategy(
        string $strategy, 
        array $platformData, 
        array $performance, 
        float $currentLoad, 
        array $config
    ): float {
        switch ($strategy) {
            case 'cost_optimized':
                return $this->calculateCostOptimizedWeight($platformData, $performance, $currentLoad);
                
            case 'quality_first':
                return $this->calculateQualityFirstWeight($platformData, $performance, $currentLoad);
                
            case 'speed_first':
                return $this->calculateSpeedFirstWeight($platformData, $performance, $currentLoad);
                
            case 'balanced':
                return $this->calculateBalancedWeight($platformData, $performance, $currentLoad);
                
            case 'priority_based':
                return $this->calculatePriorityBasedWeight($platformData, $performance, $currentLoad);
                
            default: // performance_based
                return $this->calculatePerformanceBasedWeight($platformData, $performance, $currentLoad);
        }
    }

    /**
     * 🔧 新增：成本优化权重计算
     */
    protected function calculateCostOptimizedWeight(array $platformData, array $performance, float $currentLoad): float
    {
        $costScore = 0;
        foreach ($platformData['models'] as $model) {
            $costScore += (10 - min($model['cost_per_request'] * 1000, 10)) / 10;
        }
        $avgCostScore = $costScore / count($platformData['models']);
        
        $availabilityScore = ($performance['availability'] ?? 95) / 100;
        $loadScore = 1 - min($currentLoad, 0.9);
        
        return ($avgCostScore * 0.6) + ($availabilityScore * 0.2) + ($loadScore * 0.2);
    }

    /**
     * 🔧 新增：质量优先权重计算
     */
    protected function calculateQualityFirstWeight(array $platformData, array $performance, float $currentLoad): float
    {
        $qualityScore = ($performance['success_rate'] ?? 90) / 100;
        $responseTimeScore = 1 - min(($performance['avg_response_time'] ?? 3) / 10, 1);
        $availabilityScore = ($performance['availability'] ?? 95) / 100;
        $loadScore = 1 - min($currentLoad, 0.9);
        
        return ($qualityScore * 0.4) + ($responseTimeScore * 0.3) + ($availabilityScore * 0.2) + ($loadScore * 0.1);
    }

    /**
     * 🔧 新增：速度优先权重计算
     */
    protected function calculateSpeedFirstWeight(array $platformData, array $performance, float $currentLoad): float
    {
        $responseTimeScore = 1 - min(($performance['avg_response_time'] ?? 3) / 10, 1);
        $loadScore = 1 - min($currentLoad, 0.9);
        $availabilityScore = ($performance['availability'] ?? 95) / 100;
        
        return ($responseTimeScore * 0.5) + ($loadScore * 0.3) + ($availabilityScore * 0.2);
    }

    /**
     * 🔧 新增：平衡权重计算
     */
    protected function calculateBalancedWeight(array $platformData, array $performance, float $currentLoad): float
    {
        $qualityScore = ($performance['success_rate'] ?? 90) / 100;
        $responseTimeScore = 1 - min(($performance['avg_response_time'] ?? 3) / 10, 1);
        $availabilityScore = ($performance['availability'] ?? 95) / 100;
        $loadScore = 1 - min($currentLoad, 0.9);
        
        // 平衡各项指标
        return ($qualityScore * 0.25) + ($responseTimeScore * 0.25) + ($availabilityScore * 0.25) + ($loadScore * 0.25);
    }

    /**
     * 🔧 新增：优先级权重计算
     */
    protected function calculatePriorityBasedWeight(array $platformData, array $performance, float $currentLoad): float
    {
        $priorityScore = max(0, (10 - $platformData['priority']) / 10);
        $availabilityScore = ($performance['availability'] ?? 95) / 100;
        $loadScore = 1 - min($currentLoad, 0.9);
        
        return ($priorityScore * 0.6) + ($availabilityScore * 0.2) + ($loadScore * 0.2);
    }

    /**
     * 🔧 新增：性能权重计算
     */
    protected function calculatePerformanceBasedWeight(array $platformData, array $performance, float $currentLoad): float
    {
        $successRate = ($performance['success_rate'] ?? 90) / 100;
        $availability = ($performance['availability'] ?? 95) / 100;
        $loadScore = 1 - min($currentLoad, 0.9);
        
        return ($successRate * 0.4) + ($availability * 0.4) + ($loadScore * 0.2);
    }

    /**
     * 获取当前平台负载
     */
    protected function getCurrentPlatformLoad(string $platform): float
    {
        // 🔧 修正：优化缓存键命名
        $cacheKey = "ai_platform_load_{$platform}";
        
        return Cache::remember($cacheKey, 60, function () use ($platform) {
            // 🔧 修正：基于多个时间窗口计算负载
            $hourlyRequests = Cache::get("ai_platform_requests_{$platform}_" . now()->format('Y-m-d-H'), 0);
            $minutelyRequests = Cache::get("ai_platform_requests_{$platform}_" . now()->format('Y-m-d-H-i'), 0);
            
            // 假设每小时最大处理能力
            $maxHourlyCapacity = $this->getPlatformMaxCapacity($platform);
            $maxMinutelyCapacity = $maxHourlyCapacity / 60;
            
            $hourlyLoad = min($hourlyRequests / $maxHourlyCapacity, 1.0);
            $minutelyLoad = min($minutelyRequests / $maxMinutelyCapacity, 1.0);
            
            // 取较高的负载值作为当前负载
            return max($hourlyLoad, $minutelyLoad);
        });
    }

    /**
     * 🔧 新增：获取平台最大处理能力
     */
    protected function getPlatformMaxCapacity(string $platform): int
    {
        $capacities = [
            'deepseek' => 200,    // 每小时200个请求
            'liblib' => 150,     // 每小时150个请求
            'kling' => 100,      // 每小时100个请求
            'minimax' => 300,    // 每小时300个请求
            'volcengine' => 250  // 每小时250个请求
        ];

        return $capacities[$platform] ?? 100;
    }

    /**
     * 分配任务
     */
    protected function allocateTasks(array $tasks, array $platformWeights, array $config): array
    {
        $distribution = [];
        $totalWeight = array_sum(array_column($platformWeights, 'weight'));

        if ($totalWeight <= 0) {
            Log::error('所有平台权重为0，无法分配任务', [
                'platform_weights' => $platformWeights
            ]);
            return [];
        }

        // 计算每个平台应分配的任务数
        $taskCount = count($tasks);
        $allocations = [];
        $minTasksPerPlatform = $config['min_tasks_per_platform'] ?? 1;

        foreach ($platformWeights as $platform => $data) {
            $ratio = $data['weight'] / $totalWeight;
            $allocatedCount = max((int) round($taskCount * $ratio), $minTasksPerPlatform);
            $allocations[$platform] = $allocatedCount;
        }

        // 调整分配数量确保总数匹配
        $totalAllocated = array_sum($allocations);
        if ($totalAllocated !== $taskCount) {
            $diff = $taskCount - $totalAllocated;
            // 将差值分配给权重最高的平台
            $bestPlatform = array_keys($platformWeights)[0];
            $allocations[$bestPlatform] = max(0, $allocations[$bestPlatform] + $diff);
        }

        // 分配具体任务
        $taskIndex = 0;
        foreach ($allocations as $platform => $count) {
            if ($count > 0 && $taskIndex < $taskCount) {
                $actualCount = min($count, $taskCount - $taskIndex);
                $platformTasks = array_slice($tasks, $taskIndex, $actualCount);

                $distribution[$platform] = [
                    'platform' => $platform,
                    'task_count' => $actualCount,
                    'tasks' => $platformTasks,
                    'weight' => $platformWeights[$platform]['weight'],
                    'priority' => $platformWeights[$platform]['priority'],
                    'estimated_completion_time' => $this->estimateCompletionTime($platform, $actualCount),
                    'estimated_cost' => $this->estimateCost($platform, $actualCount),
                    'load_percentage' => round(($platformWeights[$platform]['current_load'] * 100), 2)
                ];
                $taskIndex += $actualCount;
            }
        }

        return $distribution;
    }

    /**
     * 估算完成时间
     */
    protected function estimateCompletionTime(string $platform, int $taskCount): string
    {
        // 🔧 修正：基于平台性能和当前负载估算完成时间
        $baseProcessingTimes = [
            'deepseek' => 30,    // 30秒/任务
            'liblib' => 45,     // 45秒/任务
            'kling' => 60,      // 60秒/任务
            'minimax' => 25,    // 25秒/任务
            'volcengine' => 20  // 20秒/任务
        ];

        $baseTimePerTask = $baseProcessingTimes[$platform] ?? 40;

        // 根据当前负载调整处理时间
        $currentLoad = $this->getCurrentPlatformLoad($platform);
        $loadMultiplier = 1 + ($currentLoad * 0.5); // 负载越高，处理时间越长

        $adjustedTimePerTask = $baseTimePerTask * $loadMultiplier;
        $totalSeconds = $adjustedTimePerTask * $taskCount;

        return now()->addSeconds($totalSeconds)->toISOString();
    }

    /**
     * 🔧 新增：估算成本
     */
    protected function estimateCost(string $platform, int $taskCount): float
    {
        try {
            $model = AiModelConfig::where('platform', $platform)
                ->where('is_active', true)
                ->first();

            if (!$model) {
                return 0.0;
            }

            return round($model->cost_per_request * $taskCount, 4);

        } catch (\Exception $e) {
            Log::error('估算平台成本失败', [
                'platform' => $platform,
                'task_count' => $taskCount,
                'error' => $e->getMessage()
            ]);
            return 0.0;
        }
    }

    /**
     * 🔧 新增：计算整体完成时间
     */
    protected function calculateOverallCompletionTime(array $distribution): string
    {
        $latestTime = now();

        foreach ($distribution as $data) {
            $completionTime = \Carbon\Carbon::parse($data['estimated_completion_time']);
            if ($completionTime->gt($latestTime)) {
                $latestTime = $completionTime;
            }
        }

        return $latestTime->toISOString();
    }

    /**
     * 🔧 新增：验证分配结果
     */
    protected function validateDistribution(array $distribution, array $originalTasks): array
    {
        $totalDistributedTasks = 0;
        $distributedTaskIds = [];

        foreach ($distribution as $platformData) {
            $totalDistributedTasks += $platformData['task_count'];

            foreach ($platformData['tasks'] as $task) {
                $taskId = $task['id'] ?? $task['task_id'] ?? null;
                if ($taskId) {
                    if (in_array($taskId, $distributedTaskIds)) {
                        return [
                            'valid' => false,
                            'error' => "任务ID {$taskId} 重复分配"
                        ];
                    }
                    $distributedTaskIds[] = $taskId;
                }
            }
        }

        if ($totalDistributedTasks !== count($originalTasks)) {
            return [
                'valid' => false,
                'error' => "分配任务数量不匹配：原始 " . count($originalTasks) . "，分配 {$totalDistributedTasks}"
            ];
        }

        return ['valid' => true];
    }

    /**
     * 记录分配结果
     */
    protected function recordDistribution(array $distribution, string $businessType, int $userId): void
    {
        Log::info('AI负载均衡分配记录', [
            'event_type' => 'load_balancing_distribution',
            'user_id' => $userId,
            'business_type' => $businessType,
            'platforms_used' => array_keys($distribution),
            'task_distribution' => array_map(function ($data) {
                return [
                    'platform' => $data['platform'],
                    'task_count' => $data['task_count'],
                    'weight' => $data['weight'],
                    'priority' => $data['priority'],
                    'estimated_cost' => $data['estimated_cost'] ?? 0,
                    'load_percentage' => $data['load_percentage'] ?? 0
                ];
            }, $distribution),
            'total_tasks' => array_sum(array_column($distribution, 'task_count')),
            'total_estimated_cost' => array_sum(array_column($distribution, 'estimated_cost')),
            'timestamp' => now()->toISOString()
        ]);

        // 更新平台请求计数
        foreach ($distribution as $data) {
            $platform = $data['platform'];
            $taskCount = $data['task_count'];

            // 小时级计数
            $hourlyKey = "ai_platform_requests_{$platform}_" . now()->format('Y-m-d-H');
            Cache::increment($hourlyKey, $taskCount);
            Cache::expire($hourlyKey, 3600);

            // 分钟级计数
            $minutelyKey = "ai_platform_requests_{$platform}_" . now()->format('Y-m-d-H-i');
            Cache::increment($minutelyKey, $taskCount);
            Cache::expire($minutelyKey, 60);
        }
    }
}
