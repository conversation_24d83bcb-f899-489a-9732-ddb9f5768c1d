{"timestamp": "2025-08-01 22:55:41", "summary": {"total_controllers": 41, "total_methods": 253, "methods_with_try_catch": 240, "methods_without_try_catch": 13, "controllers_with_log_import": 40, "controllers_without_log_import": 1, "valid_catch_blocks": 0, "invalid_catch_blocks": 253}, "completion_rates": {"method_try_catch": 94.86, "controller_log_import": 97.56}, "controllers": [{"file": "AdController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AdController.php", "has_log_import": 1, "methods": [{"name": "ad_store", "line_number": 38, "has_try_catch": true, "try_line": 40, "catch_line": 77, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "ad_update", "line_number": 106, "has_try_catch": true, "try_line": 108, "catch_line": 145, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "store", "line_number": 164, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "update", "line_number": 210, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}], "summary": {"total_methods": 4, "methods_with_try_catch": 2, "methods_without_try_catch": 2, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "AiGenerationController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AiGenerationController.php", "has_log_import": 1, "methods": [{"name": "generateText", "line_number": 56, "has_try_catch": true, "try_line": 58, "catch_line": 115, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getTaskStatus", "line_number": 162, "has_try_catch": true, "try_line": 164, "catch_line": 183, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getUserTasks", "line_number": 231, "has_try_catch": true, "try_line": 233, "catch_line": 266, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "retryTask", "line_number": 298, "has_try_catch": true, "try_line": 300, "catch_line": 319, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "AiModelController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AiModelController.php", "has_log_import": 1, "methods": [{"name": "available", "line_number": 97, "has_try_catch": true, "try_line": 99, "catch_line": 108, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "detail", "line_number": 191, "has_try_catch": true, "try_line": 193, "catch_line": 208, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "test", "line_number": 254, "has_try_catch": true, "try_line": 256, "catch_line": 264, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "usageStats", "line_number": 342, "has_try_catch": true, "try_line": 344, "catch_line": 372, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "favorite", "line_number": 407, "has_try_catch": true, "try_line": 409, "catch_line": 439, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "favorites", "line_number": 482, "has_try_catch": true, "try_line": 484, "catch_line": 512, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "list", "line_number": 531, "has_try_catch": true, "try_line": 533, "catch_line": 553, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "switch", "line_number": 572, "has_try_catch": true, "try_line": 574, "catch_line": 580, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "selectOptimalPlatform", "line_number": 627, "has_try_catch": true, "try_line": 629, "catch_line": 649, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "checkPlatformHealth", "line_number": 668, "has_try_catch": true, "try_line": 670, "catch_line": 674, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getAllPlatformsHealth", "line_number": 692, "has_try_catch": true, "try_line": 694, "catch_line": 698, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getPlatformStats", "line_number": 716, "has_try_catch": true, "try_line": 718, "catch_line": 723, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "platformComparison", "line_number": 777, "has_try_catch": true, "try_line": 779, "catch_line": 817, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "businessPlatforms", "line_number": 856, "has_try_catch": true, "try_line": 858, "catch_line": 889, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 14, "methods_with_try_catch": 14, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 14}}, {"file": "AiTaskController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AiTaskController.php", "has_log_import": 1, "methods": [{"name": "index", "line_number": 60, "has_try_catch": true, "try_line": 62, "catch_line": 94, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "show", "line_number": 137, "has_try_catch": true, "try_line": 139, "catch_line": 155, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "stats", "line_number": 199, "has_try_catch": true, "try_line": 201, "catch_line": 225, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "cancel", "line_number": 255, "has_try_catch": true, "try_line": 257, "catch_line": 281, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "retry", "line_number": 313, "has_try_catch": true, "try_line": 315, "catch_line": 341, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchStatus", "line_number": 387, "has_try_catch": true, "try_line": 389, "catch_line": 414, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "recovery", "line_number": 451, "has_try_catch": true, "try_line": 453, "catch_line": 469, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "timeoutConfig", "line_number": 501, "has_try_catch": true, "try_line": 503, "catch_line": 517, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 8, "methods_with_try_catch": 8, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}, {"file": "AnalyticsController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AnalyticsController.php", "has_log_import": 1, "methods": [{"name": "getUserBehavior", "line_number": 67, "has_try_catch": true, "try_line": 69, "catch_line": 103, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getSystemUsage", "line_number": 181, "has_try_catch": true, "try_line": 183, "catch_line": 220, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getAiPerformance", "line_number": 282, "has_try_catch": true, "try_line": 284, "catch_line": 320, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getUserRetention", "line_number": 380, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "getRevenue", "line_number": 468, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "generateCustomReport", "line_number": 529, "has_try_catch": true, "try_line": 531, "catch_line": 586, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 6, "methods_with_try_catch": 4, "methods_without_try_catch": 2, "valid_catch_blocks": 0, "invalid_catch_blocks": 6}}, {"file": "AssetController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AssetController.php", "has_log_import": 1, "methods": [{"name": "list", "line_number": 34, "has_try_catch": true, "try_line": 36, "catch_line": 48, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "upload", "line_number": 59, "has_try_catch": true, "try_line": 61, "catch_line": 92, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "show", "line_number": 105, "has_try_catch": true, "try_line": 107, "catch_line": 117, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 128, "has_try_catch": true, "try_line": 130, "catch_line": 140, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "AudioController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AudioController.php", "has_log_import": 1, "methods": [{"name": "mix", "line_number": 46, "has_try_catch": true, "try_line": 48, "catch_line": 98, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getMixStatus", "line_number": 137, "has_try_catch": true, "try_line": 139, "catch_line": 154, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "enhance", "line_number": 188, "has_try_catch": true, "try_line": 190, "catch_line": 240, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getEnhanceStatus", "line_number": 279, "has_try_catch": true, "try_line": 281, "catch_line": 296, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "AuthController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\AuthController.php", "has_log_import": 1, "methods": [{"name": "register", "line_number": 40, "has_try_catch": true, "try_line": 42, "catch_line": 71, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "login", "line_number": 107, "has_try_catch": true, "try_line": 109, "catch_line": 131, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "logout", "line_number": 158, "has_try_catch": true, "try_line": 160, "catch_line": 175, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "forgotPassword", "line_number": 202, "has_try_catch": true, "try_line": 204, "catch_line": 223, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "resetPassword", "line_number": 252, "has_try_catch": true, "try_line": 254, "catch_line": 278, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "verify", "line_number": 307, "has_try_catch": true, "try_line": 309, "catch_line": 318, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 6, "methods_with_try_catch": 6, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 6}}, {"file": "BatchController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\BatchController.php", "has_log_import": 1, "methods": [{"name": "generateImages", "line_number": 47, "has_try_catch": true, "try_line": 49, "catch_line": 96, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "synthesizeVoices", "line_number": 130, "has_try_catch": true, "try_line": 132, "catch_line": 179, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "generateMusic", "line_number": 212, "has_try_catch": true, "try_line": 214, "catch_line": 259, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getBatchStatus", "line_number": 302, "has_try_catch": true, "try_line": 304, "catch_line": 320, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "cancelBatch", "line_number": 349, "has_try_catch": true, "try_line": 351, "catch_line": 367, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "generateResources", "line_number": 402, "has_try_catch": true, "try_line": 404, "catch_line": 449, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getResourcesStatus", "line_number": 479, "has_try_catch": true, "try_line": 481, "catch_line": 513, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "CacheController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\CacheController.php", "has_log_import": 0, "methods": [{"name": "getStats", "line_number": 74, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "clearCache", "line_number": 120, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "warmupCache", "line_number": 182, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "get<PERSON><PERSON><PERSON>", "line_number": 264, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "getValue", "line_number": 332, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "setValue", "line_number": 394, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "deleteKeys", "line_number": 476, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "getConfig", "line_number": 565, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}], "summary": {"total_methods": 8, "methods_with_try_catch": 0, "methods_without_try_catch": 8, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}, {"file": "CharacterController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\CharacterController.php", "has_log_import": 1, "methods": [{"name": "getCategories", "line_number": 53, "has_try_catch": true, "try_line": 55, "catch_line": 79, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getLibrary", "line_number": 133, "has_try_catch": true, "try_line": 135, "catch_line": 162, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getCharacterDetail", "line_number": 211, "has_try_catch": true, "try_line": 213, "catch_line": 234, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "generate", "line_number": 284, "has_try_catch": true, "try_line": 286, "catch_line": 337, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getRecommendations", "line_number": 378, "has_try_catch": true, "try_line": 380, "catch_line": 398, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "bindCharacter", "line_number": 417, "has_try_catch": true, "try_line": 419, "catch_line": 460, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getMyBindings", "line_number": 478, "has_try_catch": true, "try_line": 480, "catch_line": 505, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "updateBinding", "line_number": 524, "has_try_catch": true, "try_line": 526, "catch_line": 576, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "unbindCharacter", "line_number": 595, "has_try_catch": true, "try_line": 597, "catch_line": 644, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 9, "methods_with_try_catch": 9, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 9}}, {"file": "ConfigController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\ConfigController.php", "has_log_import": 1, "methods": [{"name": "index", "line_number": 60, "has_try_catch": true, "try_line": 62, "catch_line": 98, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getPublicConfig", "line_number": 139, "has_try_catch": true, "try_line": 141, "catch_line": 149, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "update", "line_number": 181, "has_try_catch": true, "try_line": 183, "catch_line": 214, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchUpdate", "line_number": 249, "has_try_catch": true, "try_line": 251, "catch_line": 290, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "reset", "line_number": 321, "has_try_catch": true, "try_line": 323, "catch_line": 344, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "history", "line_number": 393, "has_try_catch": true, "try_line": 395, "catch_line": 424, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "validateConfig", "line_number": 454, "has_try_catch": true, "try_line": 456, "catch_line": 490, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "CreditsController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\CreditsController.php", "has_log_import": 1, "methods": [{"name": "checkCredits", "line_number": 51, "has_try_catch": true, "try_line": 53, "catch_line": 90, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "freezeCredits", "line_number": 130, "has_try_catch": true, "try_line": 132, "catch_line": 173, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "refundCredits", "line_number": 211, "has_try_catch": true, "try_line": 213, "catch_line": 248, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 3, "methods_with_try_catch": 3, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 3}}, {"file": "DownloadController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\DownloadController.php", "has_log_import": 1, "methods": [{"name": "list", "line_number": 73, "has_try_catch": true, "try_line": 75, "catch_line": 119, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "retry", "line_number": 153, "has_try_catch": true, "try_line": 155, "catch_line": 170, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "statistics", "line_number": 217, "has_try_catch": true, "try_line": 219, "catch_line": 247, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "createLink", "line_number": 287, "has_try_catch": true, "try_line": 289, "catch_line": 331, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "secureDownload", "line_number": 354, "has_try_catch": true, "try_line": 356, "catch_line": 364, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchDownload", "line_number": 401, "has_try_catch": true, "try_line": 403, "catch_line": 446, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "cleanup", "line_number": 480, "has_try_catch": true, "try_line": 482, "catch_line": 515, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "FileController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\FileController.php", "has_log_import": 1, "methods": [{"name": "upload", "line_number": 51, "has_try_catch": true, "try_line": 53, "catch_line": 89, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getFiles", "line_number": 139, "has_try_catch": true, "try_line": 141, "catch_line": 165, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getFileDetail", "line_number": 205, "has_try_catch": true, "try_line": 207, "catch_line": 222, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "deleteFile", "line_number": 253, "has_try_catch": true, "try_line": 255, "catch_line": 270, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "downloadFile", "line_number": 302, "has_try_catch": true, "try_line": 304, "catch_line": 319, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 5, "methods_with_try_catch": 5, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 5}}, {"file": "ImageController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\ImageController.php", "has_log_import": 1, "methods": [{"name": "generate", "line_number": 57, "has_try_catch": true, "try_line": 59, "catch_line": 83, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 153, "has_try_catch": true, "try_line": 155, "catch_line": 170, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getResult", "line_number": 212, "has_try_catch": true, "try_line": 214, "catch_line": 229, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchGenerate", "line_number": 264, "has_try_catch": true, "try_line": 266, "catch_line": 286, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "LogController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\LogController.php", "has_log_import": 1, "methods": [{"name": "systemLogs", "line_number": 75, "has_try_catch": true, "try_line": 77, "catch_line": 119, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "userActionLogs", "line_number": 184, "has_try_catch": true, "try_line": 186, "catch_line": 231, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "aiCallLogs", "line_number": 299, "has_try_catch": true, "try_line": 301, "catch_line": 343, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "errorLogs", "line_number": 410, "has_try_catch": true, "try_line": 412, "catch_line": 454, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "resolveError", "line_number": 486, "has_try_catch": true, "try_line": 488, "catch_line": 517, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "exportLogs", "line_number": 549, "has_try_catch": true, "try_line": 551, "catch_line": 592, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 6, "methods_with_try_catch": 6, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 6}}, {"file": "MusicController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\MusicController.php", "has_log_import": 1, "methods": [{"name": "generate", "line_number": 53, "has_try_catch": true, "try_line": 55, "catch_line": 112, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 152, "has_try_catch": true, "try_line": 154, "catch_line": 169, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getResult", "line_number": 214, "has_try_catch": true, "try_line": 216, "catch_line": 231, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchGenerate", "line_number": 266, "has_try_catch": true, "try_line": 268, "catch_line": 308, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "NotificationController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\NotificationController.php", "has_log_import": 1, "methods": [{"name": "index", "line_number": 60, "has_try_catch": true, "try_line": 62, "catch_line": 94, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "mark<PERSON><PERSON><PERSON>", "line_number": 122, "has_try_catch": true, "try_line": 124, "catch_line": 156, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "markAllAsRead", "line_number": 183, "has_try_catch": true, "try_line": 185, "catch_line": 209, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "destroy", "line_number": 236, "has_try_catch": true, "try_line": 238, "catch_line": 254, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "stats", "line_number": 295, "has_try_catch": true, "try_line": 297, "catch_line": 313, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "send", "line_number": 345, "has_try_catch": true, "try_line": 347, "catch_line": 397, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 6, "methods_with_try_catch": 6, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 6}}, {"file": "PermissionController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\PermissionController.php", "has_log_import": 1, "methods": [{"name": "getUserPermissions", "line_number": 72, "has_try_catch": true, "try_line": 74, "catch_line": 104, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "checkPermission", "line_number": 141, "has_try_catch": true, "try_line": 143, "catch_line": 180, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getRoles", "line_number": 237, "has_try_catch": true, "try_line": 239, "catch_line": 260, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "assignRole", "line_number": 296, "has_try_catch": true, "try_line": 298, "catch_line": 344, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "grantPermissions", "line_number": 383, "has_try_catch": true, "try_line": 385, "catch_line": 434, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "revokePermissions", "line_number": 469, "has_try_catch": true, "try_line": 471, "catch_line": 517, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getPermissionHistory", "line_number": 566, "has_try_catch": true, "try_line": 568, "catch_line": 604, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "PointsController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\PointsController.php", "has_log_import": 1, "methods": [{"name": "balance", "line_number": 48, "has_try_catch": true, "try_line": 50, "catch_line": 64, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "recharge", "line_number": 100, "has_try_catch": true, "try_line": 102, "catch_line": 133, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "transactions", "line_number": 181, "has_try_catch": true, "try_line": 183, "catch_line": 218, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 3, "methods_with_try_catch": 3, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 3}}, {"file": "ProjectController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\ProjectController.php", "has_log_import": 1, "methods": [{"name": "createWithStory", "line_number": 52, "has_try_catch": true, "try_line": 54, "catch_line": 105, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "confirmTitle", "line_number": 140, "has_try_catch": true, "try_line": 142, "catch_line": 175, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "myProjects", "line_number": 223, "has_try_catch": true, "try_line": 225, "catch_line": 272, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "detail", "line_number": 315, "has_try_catch": true, "try_line": 317, "catch_line": 355, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "list", "line_number": 371, "has_try_catch": true, "try_line": 373, "catch_line": 375, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "create", "line_number": 391, "has_try_catch": true, "try_line": 393, "catch_line": 417, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "show", "line_number": 435, "has_try_catch": true, "try_line": 437, "catch_line": 439, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "update", "line_number": 455, "has_try_catch": true, "try_line": 457, "catch_line": 485, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 503, "has_try_catch": true, "try_line": 505, "catch_line": 524, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 9, "methods_with_try_catch": 9, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 9}}, {"file": "ProjectManagementController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\ProjectManagementController.php", "has_log_import": 1, "methods": [{"name": "createTask", "line_number": 55, "has_try_catch": true, "try_line": 57, "catch_line": 104, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "collaborate", "line_number": 144, "has_try_catch": true, "try_line": 146, "catch_line": 179, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getProgress", "line_number": 227, "has_try_catch": true, "try_line": 229, "catch_line": 250, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "assignResources", "line_number": 294, "has_try_catch": true, "try_line": 296, "catch_line": 324, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatistics", "line_number": 371, "has_try_catch": true, "try_line": 373, "catch_line": 394, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getMilestones", "line_number": 443, "has_try_catch": true, "try_line": 445, "catch_line": 466, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 6, "methods_with_try_catch": 6, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 6}}, {"file": "PublicationController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\PublicationController.php", "has_log_import": 1, "methods": [{"name": "publish", "line_number": 58, "has_try_catch": true, "try_line": 60, "catch_line": 115, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 156, "has_try_catch": true, "try_line": 158, "catch_line": 173, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "update", "line_number": 212, "has_try_catch": true, "try_line": 214, "catch_line": 243, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 275, "has_try_catch": true, "try_line": 277, "catch_line": 292, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "unpublish", "line_number": 324, "has_try_catch": true, "try_line": 326, "catch_line": 341, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "myPublications", "line_number": 398, "has_try_catch": true, "try_line": 400, "catch_line": 432, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "plaza", "line_number": 500, "has_try_catch": true, "try_line": 502, "catch_line": 530, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "detail", "line_number": 592, "has_try_catch": true, "try_line": 594, "catch_line": 603, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 8, "methods_with_try_catch": 8, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}, {"file": "RecommendationController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\RecommendationController.php", "has_log_import": 1, "methods": [{"name": "content", "line_number": 78, "has_try_catch": true, "try_line": 80, "catch_line": 112, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "users", "line_number": 177, "has_try_catch": true, "try_line": 179, "catch_line": 207, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "topics", "line_number": 268, "has_try_catch": true, "try_line": 270, "catch_line": 294, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "feedback", "line_number": 333, "has_try_catch": true, "try_line": 335, "catch_line": 378, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "preferences", "line_number": 436, "has_try_catch": true, "try_line": 438, "catch_line": 453, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "updatePreferences", "line_number": 499, "has_try_catch": true, "try_line": 501, "catch_line": 543, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "analytics", "line_number": 600, "has_try_catch": true, "try_line": 602, "catch_line": 628, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "personalized", "line_number": 647, "has_try_catch": true, "try_line": 649, "catch_line": 668, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 8, "methods_with_try_catch": 8, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}, {"file": "ResourceController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\ResourceController.php", "has_log_import": 1, "methods": [{"name": "generate", "line_number": 59, "has_try_catch": true, "try_line": 61, "catch_line": 134, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 185, "has_try_catch": true, "try_line": 187, "catch_line": 202, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "list", "line_number": 252, "has_try_catch": true, "try_line": 254, "catch_line": 288, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 320, "has_try_catch": true, "try_line": 322, "catch_line": 337, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getDownloadInfo", "line_number": 359, "has_try_catch": true, "try_line": 361, "catch_line": 383, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "confirmDownload", "line_number": 403, "has_try_catch": true, "try_line": 405, "catch_line": 436, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "myResources", "line_number": 456, "has_try_catch": true, "try_line": 458, "catch_line": 475, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "updateStatus", "line_number": 495, "has_try_catch": true, "try_line": 497, "catch_line": 528, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 8, "methods_with_try_catch": 8, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}, {"file": "ReviewController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\ReviewController.php", "has_log_import": 1, "methods": [{"name": "submit", "line_number": 50, "has_try_catch": true, "try_line": 52, "catch_line": 89, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 136, "has_try_catch": true, "try_line": 138, "catch_line": 153, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "appeal", "line_number": 190, "has_try_catch": true, "try_line": 192, "catch_line": 227, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "myReviews", "line_number": 287, "has_try_catch": true, "try_line": 289, "catch_line": 325, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "queueStatus", "line_number": 374, "has_try_catch": true, "try_line": 376, "catch_line": 384, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "guidelines", "line_number": 459, "has_try_catch": true, "try_line": 461, "catch_line": 469, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "line_number": 512, "has_try_catch": true, "try_line": 514, "catch_line": 547, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "SocialController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\SocialController.php", "has_log_import": 1, "methods": [{"name": "follow", "line_number": 49, "has_try_catch": true, "try_line": 51, "catch_line": 94, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "follows", "line_number": 156, "has_try_catch": true, "try_line": 158, "catch_line": 184, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "like", "line_number": 221, "has_try_catch": true, "try_line": 223, "catch_line": 261, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "comment", "line_number": 307, "has_try_catch": true, "try_line": 309, "catch_line": 351, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "comments", "line_number": 426, "has_try_catch": true, "try_line": 428, "catch_line": 456, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "share", "line_number": 495, "has_try_catch": true, "try_line": 497, "catch_line": 538, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "feed", "line_number": 600, "has_try_catch": true, "try_line": 602, "catch_line": 632, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "notifications", "line_number": 691, "has_try_catch": true, "try_line": 693, "catch_line": 725, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "markNotificationsRead", "line_number": 757, "has_try_catch": true, "try_line": 759, "catch_line": 783, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 9, "methods_with_try_catch": 9, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 9}}, {"file": "SoundController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\SoundController.php", "has_log_import": 1, "methods": [{"name": "generate", "line_number": 53, "has_try_catch": true, "try_line": 55, "catch_line": 117, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 157, "has_try_catch": true, "try_line": 159, "catch_line": 174, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getResult", "line_number": 219, "has_try_catch": true, "try_line": 221, "catch_line": 236, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchGenerate", "line_number": 271, "has_try_catch": true, "try_line": 273, "catch_line": 313, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "StoryController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\StoryController.php", "has_log_import": 1, "methods": [{"name": "generate", "line_number": 71, "has_try_catch": true, "try_line": 73, "catch_line": 122, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 162, "has_try_catch": true, "try_line": 164, "catch_line": 179, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 2, "methods_with_try_catch": 2, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 2}}, {"file": "StyleController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\StyleController.php", "has_log_import": 1, "methods": [{"name": "list", "line_number": 63, "has_try_catch": true, "try_line": 65, "catch_line": 96, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "detail", "line_number": 140, "has_try_catch": true, "try_line": 142, "catch_line": 165, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "popular", "line_number": 201, "has_try_catch": true, "try_line": 203, "catch_line": 211, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "create", "line_number": 230, "has_try_catch": true, "try_line": 232, "catch_line": 262, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "TaskManagementController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\TaskManagementController.php", "has_log_import": 1, "methods": [{"name": "cancelTask", "line_number": 48, "has_try_catch": true, "try_line": 50, "catch_line": 87, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "retryTask", "line_number": 124, "has_try_catch": true, "try_line": 126, "catch_line": 159, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getBatchStatus", "line_number": 209, "has_try_catch": true, "try_line": 211, "catch_line": 251, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getTimeoutConfig", "line_number": 281, "has_try_catch": true, "try_line": 283, "catch_line": 298, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getRecoveryStatus", "line_number": 327, "has_try_catch": true, "try_line": 329, "catch_line": 351, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 5, "methods_with_try_catch": 5, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 5}}, {"file": "TemplateController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\TemplateController.php", "has_log_import": 1, "methods": [{"name": "create", "line_number": 69, "has_try_catch": true, "try_line": 71, "catch_line": 128, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "use", "line_number": 176, "has_try_catch": true, "try_line": 178, "catch_line": 214, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "marketplace", "line_number": 289, "has_try_catch": true, "try_line": 291, "catch_line": 321, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "myTemplates", "line_number": 381, "has_try_catch": true, "try_line": 383, "catch_line": 417, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "detail", "line_number": 485, "has_try_catch": true, "try_line": 487, "catch_line": 496, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "update", "line_number": 542, "has_try_catch": true, "try_line": 544, "catch_line": 572, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 603, "has_try_catch": true, "try_line": 605, "catch_line": 620, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "UserController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\UserController.php", "has_log_import": 1, "methods": [{"name": "profile", "line_number": 57, "has_try_catch": true, "try_line": 59, "catch_line": 83, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "updateProfile", "line_number": 102, "has_try_catch": true, "try_line": 104, "catch_line": 142, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "updatePreferences", "line_number": 186, "has_try_catch": true, "try_line": 188, "catch_line": 230, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getPreferences", "line_number": 268, "has_try_catch": true, "try_line": 270, "catch_line": 299, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "UserGrowthController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\UserGrowthController.php", "has_log_import": 1, "methods": [{"name": "profile", "line_number": 74, "has_try_catch": true, "try_line": 76, "catch_line": 91, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "leaderboard", "line_number": 138, "has_try_catch": true, "try_line": 140, "catch_line": 170, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "completeAchievement", "line_number": 213, "has_try_catch": true, "try_line": 215, "catch_line": 243, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "dailyTasks", "line_number": 292, "has_try_catch": true, "try_line": 294, "catch_line": 309, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "completeDailyTask", "line_number": 351, "has_try_catch": true, "try_line": 353, "catch_line": 381, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "history", "line_number": 440, "has_try_catch": true, "try_line": 442, "catch_line": 476, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "statistics", "line_number": 531, "has_try_catch": true, "try_line": 533, "catch_line": 559, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "setGoals", "line_number": 601, "has_try_catch": true, "try_line": 603, "catch_line": 627, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "recommendations", "line_number": 681, "has_try_catch": true, "try_line": 683, "catch_line": 698, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "milestones", "line_number": 717, "has_try_catch": true, "try_line": 719, "catch_line": 738, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 10, "methods_with_try_catch": 10, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 10}}, {"file": "VersionController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\VersionController.php", "has_log_import": 1, "methods": [{"name": "create", "line_number": 53, "has_try_catch": true, "try_line": 55, "catch_line": 102, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "list", "line_number": 156, "has_try_catch": true, "try_line": 158, "catch_line": 188, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "show", "line_number": 241, "has_try_catch": true, "try_line": 243, "catch_line": 258, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "setCurrent", "line_number": 291, "has_try_catch": true, "try_line": 293, "catch_line": 308, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 340, "has_try_catch": true, "try_line": 342, "catch_line": 357, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "compare", "line_number": 410, "has_try_catch": true, "try_line": 412, "catch_line": 446, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 6, "methods_with_try_catch": 6, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 6}}, {"file": "VideoController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\VideoController.php", "has_log_import": 1, "methods": [{"name": "generate", "line_number": 57, "has_try_catch": true, "try_line": 59, "catch_line": 115, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 157, "has_try_catch": true, "try_line": 159, "catch_line": 174, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getResult", "line_number": 219, "has_try_catch": true, "try_line": 221, "catch_line": 236, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 3, "methods_with_try_catch": 3, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 3}}, {"file": "VoiceController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\VoiceController.php", "has_log_import": 1, "methods": [{"name": "synthesize", "line_number": 56, "has_try_catch": true, "try_line": 58, "catch_line": 118, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 158, "has_try_catch": true, "try_line": 160, "catch_line": 175, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "batchSynthesize", "line_number": 210, "has_try_catch": true, "try_line": 212, "catch_line": 252, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "clone", "line_number": 290, "has_try_catch": true, "try_line": 292, "catch_line": 343, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getCloneStatus", "line_number": 382, "has_try_catch": true, "try_line": 384, "catch_line": 399, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "custom", "line_number": 436, "has_try_catch": true, "try_line": 438, "catch_line": 493, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getCustomStatus", "line_number": 537, "has_try_catch": true, "try_line": 539, "catch_line": 554, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 7, "methods_with_try_catch": 7, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 7}}, {"file": "WebSocketController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\WebSocketController.php", "has_log_import": 1, "methods": [{"name": "authenticate", "line_number": 49, "has_try_catch": true, "try_line": 51, "catch_line": 97, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getSessions", "line_number": 135, "has_try_catch": true, "try_line": 137, "catch_line": 155, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "disconnect", "line_number": 187, "has_try_catch": true, "try_line": 189, "catch_line": 221, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getStatus", "line_number": 256, "has_try_catch": true, "try_line": 258, "catch_line": 266, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 4, "methods_with_try_catch": 4, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 4}}, {"file": "WorkPublishController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\WorkPublishController.php", "has_log_import": 1, "methods": [{"name": "publishWork", "line_number": 42, "has_try_catch": false, "try_line": null, "catch_line": null, "valid_catch_block": false, "catch_issues": []}, {"name": "update", "line_number": 144, "has_try_catch": true, "try_line": 146, "catch_line": 210, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "delete", "line_number": 230, "has_try_catch": true, "try_line": 232, "catch_line": 268, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "myWorks", "line_number": 288, "has_try_catch": true, "try_line": 290, "catch_line": 311, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "gallery", "line_number": 329, "has_try_catch": true, "try_line": 331, "catch_line": 341, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getShareLink", "line_number": 361, "has_try_catch": true, "try_line": 363, "catch_line": 406, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "like", "line_number": 426, "has_try_catch": true, "try_line": 428, "catch_line": 474, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "trending", "line_number": 492, "has_try_catch": true, "try_line": 494, "catch_line": 534, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 8, "methods_with_try_catch": 7, "methods_without_try_catch": 1, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}, {"file": "WorkflowController.php", "path": "D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\\WorkflowController.php", "has_log_import": 1, "methods": [{"name": "create", "line_number": 72, "has_try_catch": true, "try_line": 74, "catch_line": 126, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "index", "line_number": 176, "has_try_catch": true, "try_line": 178, "catch_line": 213, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "show", "line_number": 277, "has_try_catch": true, "try_line": 279, "catch_line": 295, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "execute", "line_number": 336, "has_try_catch": true, "try_line": 338, "catch_line": 368, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getExecutionStatus", "line_number": 428, "has_try_catch": true, "try_line": 430, "catch_line": 446, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "provideStepInput", "line_number": 479, "has_try_catch": true, "try_line": 481, "catch_line": 517, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "cancelExecution", "line_number": 551, "has_try_catch": true, "try_line": 553, "catch_line": 581, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}, {"name": "getExecutionHistory", "line_number": 640, "has_try_catch": true, "try_line": 642, "catch_line": 678, "valid_catch_block": false, "catch_issues": ["缺少Log::error调用", "缺少__METHOD__", "缺少user_id记录", "缺少request_data记录", "缺少error message记录", "缺少trace记录", "缺少标准errorResponse格式"]}], "summary": {"total_methods": 8, "methods_with_try_catch": 8, "methods_without_try_catch": 0, "valid_catch_blocks": 0, "invalid_catch_blocks": 8}}]}