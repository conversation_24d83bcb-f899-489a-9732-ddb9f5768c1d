<?php

/**
 * 手动验证try-catch架构
 * 更宽松的检测逻辑，减少误报
 */

$directory = 'D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api';

// 统计数据
$stats = [
    'total_controllers' => 0,
    'total_methods' => 0,
    'methods_without_try_catch' => 0,
    'methods_with_invalid_catch' => 0,
    'controllers_without_log' => 0
];

$issues = [
    'no_try_catch' => [],
    'invalid_catch' => [],
    'no_log_import' => []
];

function hasLogImport($content) {
    return preg_match('/use\s+.*Log.*;/i', $content) || 
           preg_match('/use\s+Illuminate\\Support\\Facades\\Log;/i', $content);
}

function findMethods($content) {
    $methods = [];
    $lines = explode("\n", $content);
    
    for ($i = 0; $i < count($lines); $i++) {
        $line = trim($lines[$i]);
        
        // 匹配public function（排除构造函数）
        if (preg_match('/^public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $line, $matches)) {
            $methodName = $matches[1];
            
            // 排除构造函数
            if ($methodName === '__construct') {
                continue;
            }
            
            $methods[] = [
                'name' => $methodName,
                'line' => $i + 1,
                'start_line' => $i
            ];
        }
    }
    
    return $methods;
}

function checkMethodTryCatch($lines, $methodStart) {
    // 查找方法体开始（第一个{）
    $methodBodyStart = null;
    for ($i = $methodStart; $i < count($lines); $i++) {
        if (strpos($lines[$i], '{') !== false) {
            $methodBodyStart = $i;
            break;
        }
    }
    
    if ($methodBodyStart === null) {
        return ['has_try' => false, 'reason' => '找不到方法体开始'];
    }
    
    // 查找方法体中的第一行有效代码
    for ($i = $methodBodyStart + 1; $i < count($lines); $i++) {
        $line = trim($lines[$i]);
        
        // 跳过空行和注释
        if (empty($line) || strpos($line, '//') === 0 || strpos($line, '/*') === 0 || strpos($line, '*') === 0) {
            continue;
        }
        
        // 检查是否是try {
        if ($line === 'try {') {
            return ['has_try' => true, 'try_line' => $i + 1];
        } else {
            return ['has_try' => false, 'reason' => "第一行代码不是try，而是: $line"];
        }
    }
    
    return ['has_try' => false, 'reason' => '方法体为空'];
}

function checkCatchBlock($content, $methodName) {
    // 简化的catch块检查
    $hasCatch = preg_match('/catch\s*\(.*?\$e.*?\)\s*\{/', $content);
    if (!$hasCatch) {
        return ['valid' => false, 'reason' => '没有catch块'];
    }
    
    $hasErrorResponse = preg_match('/errorResponse\s*\(.*?ApiCodeEnum::CONTROLLER_ERROR/i', $content);
    if (!$hasErrorResponse) {
        return ['valid' => false, 'reason' => '缺少标准errorResponse调用'];
    }
    
    return ['valid' => true];
}

// 扫描所有控制器文件
$files = glob($directory . '/*.php');

echo "开始手动验证...\n\n";

foreach ($files as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    
    $stats['total_controllers']++;
    
    // 检查Log导入
    if (!hasLogImport($content)) {
        $stats['controllers_without_log']++;
        $issues['no_log_import'][] = $filename;
    }
    
    // 查找所有方法
    $methods = findMethods($content);
    $lines = explode("\n", $content);
    
    foreach ($methods as $method) {
        $stats['total_methods']++;
        
        // 检查try-catch
        $tryCheck = checkMethodTryCatch($lines, $method['start_line']);
        
        if (!$tryCheck['has_try']) {
            $stats['methods_without_try_catch']++;
            $issues['no_try_catch'][] = [
                'controller' => $filename,
                'method' => $method['name'],
                'line' => $method['line'],
                'reason' => $tryCheck['reason']
            ];
        } else {
            // 检查catch块
            $catchCheck = checkCatchBlock($content, $method['name']);
            if (!$catchCheck['valid']) {
                $stats['methods_with_invalid_catch']++;
                $issues['invalid_catch'][] = [
                    'controller' => $filename,
                    'method' => $method['name'],
                    'line' => $method['line'],
                    'reason' => $catchCheck['reason']
                ];
            }
        }
    }
}

// 输出结果
echo "=== 手动验证结果 ===\n";
echo "总控制器数: {$stats['total_controllers']}\n";
echo "总方法数: {$stats['total_methods']}\n";
echo "缺少try-catch的方法: {$stats['methods_without_try_catch']}\n";
echo "catch块不标准的方法: {$stats['methods_with_invalid_catch']}\n";
echo "缺少Log导入的控制器: {$stats['controllers_without_log']}\n\n";

// 详细列出问题
if (!empty($issues['no_try_catch'])) {
    echo "=== 缺少try-catch的方法 ===\n";
    foreach ($issues['no_try_catch'] as $issue) {
        echo "- {$issue['controller']} -> {$issue['method']}() (第{$issue['line']}行): {$issue['reason']}\n";
    }
    echo "\n";
}

if (!empty($issues['invalid_catch'])) {
    echo "=== catch块不符合标准的方法 ===\n";
    foreach ($issues['invalid_catch'] as $issue) {
        echo "- {$issue['controller']} -> {$issue['method']}() (第{$issue['line']}行): {$issue['reason']}\n";
    }
    echo "\n";
}

if (!empty($issues['no_log_import'])) {
    echo "=== 缺少Log导入的控制器 ===\n";
    foreach ($issues['no_log_import'] as $controller) {
        echo "- $controller\n";
    }
    echo "\n";
}

echo "验证完成！\n";

?>