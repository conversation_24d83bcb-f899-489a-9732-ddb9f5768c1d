<?php

namespace App\Services\Api;

use App\Models\WebSocketSession;
use Carbon\Carbon;
use App\Models\AiGenerationTask;
use App\Models\User;
use Illuminate\Support\Facades\Log;

/**
 * WebSocket事件推送服务
 */
class WebSocketEventService
{
    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }

    /**
     * 推送AI生成进度事件
     */
    public function pushAiGenerationProgress(int $taskId, int $userId, int $progress, string $message = ''): bool
    {
        try {
            $task = AiGenerationTask::find($taskId);
            if (!$task) {
                return false;
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'progress' => $progress,
                'message' => $message,
                'timestamp' => Carbon::now()->toISOString()
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_PROGRESS,
                $eventData
            );

            Log::info('AI生成进度推送', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'progress' => $progress,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('AI生成进度推送失败', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 推送AI生成完成事件
     */
    public function pushAiGenerationCompleted(int $taskId, int $userId): bool
    {
        try {
            $task = AiGenerationTask::find($taskId);
            if (!$task) {
                return false;
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'status' => $task->status,
                'output_data' => $task->output_data,
                'cost' => $task->cost,
                'tokens_used' => $task->tokens_used,
                'processing_time_ms' => $task->processing_time_ms,
                'completed_at' => $task->completed_at?->toISOString(),
                'timestamp' => Carbon::now()->toISOString()
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_COMPLETED,
                $eventData
            );

            Log::info('AI生成完成推送', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'task_type' => $task->task_type,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('AI生成完成推送失败', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 推送AI生成失败事件
     */
    public function pushAiGenerationFailed(int $taskId, int $userId, string $errorMessage): bool
    {
        try {
            $task = AiGenerationTask::find($taskId);
            if (!$task) {
                return false;
            }

            $eventData = [
                'task_id' => $taskId,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'model_name' => $task->model_name,
                'status' => $task->status,
                'error_message' => $errorMessage,
                'retry_count' => $task->retry_count,
                'can_retry' => $task->canRetry(),
                'failed_at' => $task->completed_at?->toISOString(),
                'timestamp' => Carbon::now()->toISOString()
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_AI_GENERATION_FAILED,
                $eventData
            );

            Log::info('AI生成失败推送', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error_message' => $errorMessage,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('AI生成失败推送失败', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 推送积分变动事件
     */
    public function pushPointsChanged(int $userId, string $changeType, float $amount, float $newBalance, string $reason = ''): bool
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                return false;
            }

            $eventData = [
                'user_id' => $userId,
                'change_type' => $changeType, // 'increase', 'decrease', 'freeze', 'release'
                'amount' => $amount,
                'new_balance' => $newBalance,
                'frozen_points' => $user->frozen_points,
                'total_points' => $newBalance + $user->frozen_points,
                'reason' => $reason,
                'timestamp' => Carbon::now()->toISOString()
            ];

            $successCount = $this->webSocketService->pushToUser(
                $userId,
                WebSocketSession::EVENT_POINTS_CHANGED,
                $eventData
            );

            Log::info('积分变动推送', [
                'user_id' => $userId,
                'change_type' => $changeType,
                'amount' => $amount,
                'new_balance' => $newBalance,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('积分变动推送失败', [
                'user_id' => $userId,
                'change_type' => $changeType,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 推送自定义事件
     */
    public function pushCustomEvent(int $userId, string $eventType, array $eventData): bool
    {
        try {
            $data = array_merge($eventData, [
                'timestamp' => Carbon::now()->toISOString()
            ]);

            $successCount = $this->webSocketService->pushToUser($userId, $eventType, $data);

            Log::info('自定义事件推送', [
                'user_id' => $userId,
                'event_type' => $eventType,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('自定义事件推送失败', [
                'user_id' => $userId,
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 推送系统通知事件
     */
    public function pushSystemNotification(int $userId, string $title, string $message, string $type = 'info'): bool
    {
        try {
            $eventData = [
                'title' => $title,
                'message' => $message,
                'type' => $type, // 'info', 'success', 'warning', 'error'
                'timestamp' => Carbon::now()->toISOString()
            ];

            $successCount = $this->webSocketService->pushToUser($userId, 'system_notification', $eventData);

            Log::info('系统通知推送', [
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'success_count' => $successCount
            ]);

            return $successCount > 0;

        } catch (\Exception $e) {
            Log::error('系统通知推送失败', [
                'user_id' => $userId,
                'title' => $title,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 批量推送事件到多个用户
     */
    public function pushToMultipleUsers(array $userIds, string $eventType, array $eventData): array
    {
        $results = [];

        foreach ($userIds as $userId) {
            $results[$userId] = $this->webSocketService->pushToUser($userId, $eventType, $eventData) > 0;
        }

        $successCount = count(array_filter($results));
        
        Log::info('批量事件推送', [
            'user_count' => count($userIds),
            'event_type' => $eventType,
            'success_count' => $successCount
        ]);

        return $results;
    }

    /**
     * 推送广播事件到所有活跃连接
     */
    public function pushBroadcast(string $eventType, array $eventData): int
    {
        try {
            $activeSessions = WebSocketSession::active()->get();
            $successCount = 0;

            foreach ($activeSessions as $session) {
                if ($this->webSocketService->pushMessage($session->session_id, $eventType, $eventData)) {
                    $successCount++;
                }
            }

            Log::info('广播事件推送', [
                'event_type' => $eventType,
                'total_sessions' => $activeSessions->count(),
                'success_count' => $successCount
            ]);

            return $successCount;

        } catch (\Exception $e) {
            Log::error('广播事件推送失败', [
                'event_type' => $eventType,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }
}
