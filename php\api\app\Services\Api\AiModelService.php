<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\UserPreference;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * AI模型管理服务
 */
class AiModelService extends Service
{
    /**
     * 切换用户默认模型
     */
    public function switchUserModel(int $userId, int $modelId, string $modelType): array
    {
        try {
            DB::beginTransaction();

            $model = AiModelConfig::active()->find($modelId);
            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模型不存在或已禁用',
                    'data' => []
                ];
            }

            if ($model->model_type !== $modelType) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '模型类型不匹配',
                    'data' => []
                ];
            }

            // 获取或创建用户偏好设置
            $preference = UserPreference::firstOrCreate(['user_id' => $userId]);
            
            // 更新AI偏好中的默认模型
            $aiPreferences = $preference->ai_preferences ?? [];
            $aiPreferences['default_models'][$modelType] = $modelId;
            $preference->ai_preferences = $aiPreferences;
            $preference->save();

            DB::commit();

            Log::info('用户模型切换成功', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'model_type' => $modelType,
                'model_name' => $model->model_name,
                'platform' => $model->platform
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型切换成功',
                'data' => [
                    'model_id' => $model->id,
                    'model_name' => $model->model_name,
                    'platform' => $model->platform,
                    'model_type' => $model->model_type
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('用户模型切换失败', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'model_type' => $modelType,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模型切换失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics(?int $modelId = null, ?string $platform = null, string $timeRange = '24h'): array
    {
        try {
            $query = AiModelConfig::query();
            
            if ($modelId) {
                $query->where('id', $modelId);
            }
            
            if ($platform) {
                $query->byPlatform($platform);
            }

            $models = $query->active()->get();
            $modelsData = [];
            
            foreach ($models as $model) {
                $taskStats = $this->getModelTaskStats($model->id, $timeRange);
                
                $modelsData[] = [
                    'id' => $model->id,
                    'model_name' => $model->model_name,
                    'platform' => $model->platform,
                    'health_status' => $model->health_status,
                    'avg_response_time' => $taskStats['avg_response_time'],
                    'success_rate' => $taskStats['success_rate'],
                    'total_requests' => $taskStats['total_requests'],
                    'total_cost' => $taskStats['total_cost'],
                    'last_health_check' => $model->last_health_check?->format('Y-m-d H:i:s')
                ];
            }

            // 计算汇总数据
            $summary = [
                'total_models' => $models->count(),
                'healthy_models' => $models->where('health_status', AiModelConfig::HEALTH_HEALTHY)->count(),
                'avg_success_rate' => $models->count() > 0 ? 
                    collect($modelsData)->avg('success_rate') : 0,
                'total_requests' => collect($modelsData)->sum('total_requests')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'models' => $modelsData,
                    'summary' => $summary,
                    'time_range' => $timeRange
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取性能指标失败', [
                'model_id' => $modelId,
                'platform' => $platform,
                'time_range' => $timeRange,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取性能指标失败',
                'data' => []
            ];
        }
    }

    /**
     * 检查服务健康状态
     */
    public function checkServiceHealth(?string $platform = null, bool $forceCheck = false): array
    {
        try {
            $cacheKey = "ai_health_check_" . ($platform ?? 'all');
            
            if (!$forceCheck) {
                $cached = Cache::get($cacheKey);
                if ($cached) {
                    return $cached;
                }
            }

            $query = AiModelConfig::active();
            if ($platform) {
                $query->byPlatform($platform);
            }

            $models = $query->get();
            $platformStats = [];
            
            // 按平台分组统计
            foreach ($models->groupBy('platform') as $platformName => $platformModels) {
                $healthyCount = $platformModels->where('health_status', AiModelConfig::HEALTH_HEALTHY)->count();
                $totalCount = $platformModels->count();
                
                // 模拟健康检查（实际应该调用真实的健康检查API）
                $responseTime = $this->simulateHealthCheck($platformName);
                $status = $healthyCount === $totalCount ? 'healthy' : 
                         ($healthyCount > 0 ? 'degraded' : 'unhealthy');

                $platformStats[] = [
                    'platform' => $platformName,
                    'status' => $status,
                    'models_count' => $totalCount,
                    'healthy_models' => $healthyCount,
                    'last_check' => Carbon::now()->format('Y-m-d H:i:s'),
                    'response_time' => $responseTime
                ];
            }

            // 计算整体状态
            $totalPlatforms = count($platformStats);
            $healthyPlatforms = collect($platformStats)->where('status', 'healthy')->count();
            $overallStatus = $healthyPlatforms === $totalPlatforms ? 'healthy' : 
                           ($healthyPlatforms > 0 ? 'degraded' : 'unhealthy');

            $result = [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'overall_status' => $overallStatus,
                    'platforms' => $platformStats,
                    'summary' => [
                        'total_platforms' => $totalPlatforms,
                        'healthy_platforms' => $healthyPlatforms,
                        'total_models' => $models->count(),
                        'healthy_models' => $models->where('health_status', AiModelConfig::HEALTH_HEALTHY)->count()
                    ],
                    'check_time' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

            // 缓存结果5分钟
            Cache::put($cacheKey, $result, 300);

            return $result;

        } catch (\Exception $e) {
            Log::error('健康检查失败', [
                'platform' => $platform,
                'force_check' => $forceCheck,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '健康检查失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取用户默认模型
     */
    public function getUserDefaultModel(int $userId, string $modelType): ?AiModelConfig
    {
        try {
            $preference = UserPreference::where('user_id', $userId)->first();
            
            if ($preference && isset($preference->ai_preferences['default_models'][$modelType])) {
                $modelId = $preference->ai_preferences['default_models'][$modelType];
                return AiModelConfig::active()->find($modelId);
            }

            // 如果用户没有设置默认模型，返回系统默认模型
            return AiModelConfig::active()
                ->byType($modelType)
                ->default()
                ->first() ?: 
                AiModelConfig::active()
                    ->byType($modelType)
                    ->ordered()
                    ->first();

        } catch (\Exception $e) {
            Log::error('获取用户默认模型失败', [
                'user_id' => $userId,
                'model_type' => $modelType,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * 获取模型任务统计
     */
    private function getModelTaskStats(int $modelId, string $timeRange): array
    {
        $timeMap = [
            '1h' => Carbon::now()->subHour(),
            '24h' => Carbon::now()->subDay(),
            '7d' => Carbon::now()->subDays(7),
            '30d' => Carbon::now()->subDays(30)
        ];

        $startTime = $timeMap[$timeRange] ?? Carbon::now()->subDay();

        $tasks = AiGenerationTask::where('model_config_id', $modelId)
            ->where('created_at', '>=', $startTime)
            ->get();

        $totalRequests = $tasks->count();
        $successfulTasks = $tasks->where('status', AiGenerationTask::STATUS_COMPLETED);
        $successCount = $successfulTasks->count();
        
        return [
            'total_requests' => $totalRequests,
            'success_rate' => $totalRequests > 0 ? round($successCount / $totalRequests * 100, 2) : 0,
            'avg_response_time' => $successfulTasks->avg('processing_time_ms') ?? 0,
            'total_cost' => $tasks->sum('cost')
        ];
    }

    /**
     * 模拟健康检查
     */
    private function simulateHealthCheck(string $platform): int
    {
        // 模拟不同平台的响应时间
        $responseTimes = [
            'deepseek' => rand(100, 300),
            'liblib' => rand(200, 500),
            'kling' => rand(150, 400),
            'minimax' => rand(120, 350),
            'volcengine' => rand(180, 450)
        ];

        return $responseTimes[$platform] ?? rand(100, 500);
    }

    /**
     * 根据平台获取模型
     * 第2D1阶段：新增方法
     */
    public function getModelByPlatform(string $platform, string $modelType): ?AiModelConfig
    {
        try {
            return AiModelConfig::active()
                ->where('platform', $platform)
                ->byType($modelType)
                ->first() ?:
                AiModelConfig::active()
                    ->byType($modelType)
                    ->first();
        } catch (\Exception $e) {
            Log::error('获取平台模型失败', [
                'method' => __METHOD__,
                'platform' => $platform,
                'model_type' => $modelType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return null;
        }
    }
}
