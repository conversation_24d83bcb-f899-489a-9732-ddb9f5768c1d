@echo off
echo ========================================
echo CogniAud 控制器try-catch架构检测脚本
echo ========================================
echo.

REM 设置PHP路径（根据实际情况调整）
set PHP_PATH=D:\longtool\phpStudy_64\Extensions\php\php7.4.3nts\php.exe

REM 检查PHP是否存在
if not exist "%PHP_PATH%" (
    echo 错误: 找不到PHP可执行文件: %PHP_PATH%
    echo 请修改run_check.bat中的PHP_PATH变量
    pause
    exit /b 1
)

REM 运行检测脚本
echo 开始执行检测...
echo.
"%PHP_PATH%" check_try_catch_architecture.php

echo.
echo 检测完成！
echo 详细报告已保存到 try_catch_report.json
echo.
pause
