<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\PublicationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 作品发布与审核管理
 */
class PublicationController extends Controller
{
    protected $publicationService;

    public function __construct(PublicationService $publicationService)
    {
        $this->publicationService = $publicationService;
    }
 
    /**
     * @ApiTitle(发布作品)
     * @ApiSummary(将资源发布到作品广场)
     * @ApiMethod(POST)
     * @ApiRoute(/api/publications/publish)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="resource_id", type="int", required=true, description="资源ID")
     * @ApiParams(name="title", type="string", required=true, description="作品标题")
     * @ApiParams(name="description", type="string", required=false, description="作品描述")
     * @ApiParams(name="tags", type="array", required=false, description="标签数组")
     * @ApiParams(name="category", type="string", required=true, description="作品分类")
     * @ApiParams(name="visibility", type="string", required=false, description="可见性：public/private/unlisted")
     * @ApiParams(name="allow_comments", type="boolean", required=false, description="是否允许评论")
     * @ApiParams(name="allow_download", type="boolean", required=false, description="是否允许下载")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "作品发布成功",
     *   "data": {
     *     "publication_id": 123,
     *     "resource_id": 456,
     *     "title": "我的AI故事",
     *     "status": "pending_review",
     *     "visibility": "public",
     *     "published_at": null,
     *     "review_status": "pending",
     *     "view_count": 0,
     *     "like_count": 0,
     *     "download_count": 0
     *   }
     * })
     */
    public function publish(Request $request)
    {
        try {
            // 使用AuthService进行认证 - 优先级最高
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $rules = [
                'resource_id' => 'required|integer|exists:ai_resources,id',
                'title' => 'required|string|min:2|max:100',
                'description' => 'sometimes|string|max:1000',
                'tags' => 'sometimes|array|max:10',
                'tags.*' => 'string|max:20',
                'category' => 'required|string|in:story,image,voice,video,music,sound,mixed',
                'visibility' => 'sometimes|string|in:public,private,unlisted',
                'allow_comments' => 'sometimes|boolean',
                'allow_download' => 'sometimes|boolean'
            ];

            $messages = [
                'resource_id.required' => '资源ID不能为空',
                'resource_id.exists' => '资源不存在',
                'title.required' => '作品标题不能为空',
                'title.min' => '作品标题至少2个字符',
                'title.max' => '作品标题不能超过100个字符',
                'description.max' => '作品描述不能超过1000个字符',
                'tags.max' => '最多支持10个标签',
                'tags.*.max' => '标签长度不能超过20个字符',
                'category.required' => '作品分类不能为空',
                'category.in' => '作品分类必须是：story、image、voice、video、music、sound、mixed之一',
                'visibility.in' => '可见性必须是：public、private、unlisted之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $user = $authResult['user'];

            $publicationData = [
                'resource_id' => $request->resource_id,
                'title' => $request->title,
                'description' => $request->get('description'),
                'tags' => $request->get('tags', []),
                'category' => $request->category,
                'visibility' => $request->get('visibility', 'public'),
                'allow_comments' => $request->get('allow_comments', true),
                'allow_download' => $request->get('allow_download', true)
            ];

            $result = $this->publicationService->publishWork($user->id, $publicationData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('发布作品失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '发布作品失败', []);
        }
    }

    /**
     * @ApiTitle(获取发布状态)
     * @ApiSummary(查询作品发布状态和审核进度)
     * @ApiMethod(GET)
     * @ApiRoute(/api/publications/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="发布ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "publication_id": 123,
     *     "status": "published",
     *     "review_status": "approved",
     *     "review_message": "作品质量优秀，已通过审核",
     *     "published_at": "2024-01-01 12:00:00",
     *     "view_count": 156,
     *     "like_count": 23,
     *     "comment_count": 8,
     *     "download_count": 12,
     *     "share_count": 5,
     *     "visibility": "public",
     *     "featured": false
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->publicationService->getPublicationStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取发布状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取发布状态失败', []);
        }
    }

    /**
     * @ApiTitle(更新作品信息)
     * @ApiSummary(更新已发布作品的信息)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/publications/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="发布ID")
     * @ApiParams(name="title", type="string", required=false, description="作品标题")
     * @ApiParams(name="description", type="string", required=false, description="作品描述")
     * @ApiParams(name="tags", type="array", required=false, description="标签数组")
     * @ApiParams(name="visibility", type="string", required=false, description="可见性")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "作品信息更新成功",
     *   "data": {
     *     "publication_id": 123,
     *     "title": "我的AI故事（修订版）",
     *     "description": "更新后的描述",
     *     "tags": ["AI", "故事", "创意"],
     *     "visibility": "public",
     *     "updated_at": "2024-01-01 12:30:00"
     *   }
     * })
     */
    public function update($id, Request $request)
    {
        try {
            $rules = [
                'title' => 'sometimes|string|min:2|max:100',
                'description' => 'sometimes|string|max:1000',
                'tags' => 'sometimes|array|max:10',
                'tags.*' => 'string|max:20',
                'visibility' => 'sometimes|string|in:public,private,unlisted',
                'allow_comments' => 'sometimes|boolean',
                'allow_download' => 'sometimes|boolean'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $updateData = $request->only(['title', 'description', 'tags', 'visibility', 'allow_comments', 'allow_download']);

            $result = $this->publicationService->updatePublication($id, $user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('更新作品信息失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新作品信息失败', []);
        }
    }

    /**
     * @ApiTitle(取消发布)
     * @ApiSummary(取消发布作品，从广场移除)
     * @ApiMethod(DELETE)
     * @ApiRoute(/api/publications/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="发布ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "作品取消发布成功",
     *   "data": {
     *     "publication_id": 123,
     *     "status": "unpublished",
     *     "unpublished_at": "2024-01-01 12:45:00"
     *   }
     * })
     */
    public function delete(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->publicationService->unpublishWork($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('取消发布作品失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '取消发布作品失败', []);
        }
    }

    /**
     * @ApiTitle(取消发布 - POST方式)
     * @ApiSummary(取消发布作品，从广场移除)
     * @ApiMethod(POST)
     * @ApiRoute(/api/publications/{id}/unpublish)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="发布ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "作品取消发布成功",
     *   "data": {
     *     "publication_id": 123,
     *     "status": "unpublished",
     *     "unpublished_at": "2024-01-01 12:45:00"
     *   }
     * })
     */
    public function unpublish(Request $request, $publicationId)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->publicationService->unpublishWork($publicationId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('取消发布作品失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '取消发布作品失败', []);
        }
    }

    /**
     * @ApiTitle(我的发布列表)
     * @ApiSummary(获取用户的发布作品列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/publications/my-publications)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="status", type="string", required=false, description="状态过滤")
     * @ApiParams(name="category", type="string", required=false, description="分类过滤")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "publications": [
     *       {
     *         "publication_id": 123,
     *         "title": "我的AI故事",
     *         "category": "story",
     *         "status": "published",
     *         "view_count": 156,
     *         "like_count": 23,
     *         "published_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "statistics": {
     *       "total_publications": 5,
     *       "published_count": 3,
     *       "pending_count": 1,
     *       "rejected_count": 1,
     *       "total_views": 1250,
     *       "total_likes": 89
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 5,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function myPublications(Request $request)
    {
        try {
            $rules = [
                'status' => 'sometimes|string|in:pending_review,approved,rejected,published,unpublished',
                'category' => 'sometimes|string|in:story,image,voice,video,music,sound,mixed',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $filters = [
                'status' => $request->get('status'),
                'category' => $request->get('category'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->publicationService->getUserPublications($user->id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取我的作品列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取我的作品列表失败', []);
        }
    }

    /**
     * @ApiTitle(作品广场)
     * @ApiSummary(获取公开发布的作品列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/publications/plaza)
     * @ApiParams(name="category", type="string", required=false, description="分类过滤")
     * @ApiParams(name="sort", type="string", required=false, description="排序方式：latest/popular/trending")
     * @ApiParams(name="tags", type="string", required=false, description="标签过滤，逗号分隔")
     * @ApiParams(name="search", type="string", required=false, description="搜索关键词")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "publications": [
     *       {
     *         "publication_id": 123,
     *         "title": "精彩的AI故事",
     *         "description": "一个关于未来的故事",
     *         "category": "story",
     *         "tags": ["AI", "科幻", "未来"],
     *         "author": {
     *           "user_id": 456,
     *           "username": "创作者123",
     *           "avatar": "https://example.com/avatar.jpg"
     *         },
     *         "view_count": 1250,
     *         "like_count": 89,
     *         "comment_count": 23,
     *         "published_at": "2024-01-01 12:00:00",
     *         "featured": true,
     *         "thumbnail": "https://example.com/thumb.jpg"
     *       }
     *     ],
     *     "featured_works": [
     *       {
     *         "publication_id": 789,
     *         "title": "本周精选作品",
     *         "category": "image",
     *         "view_count": 5000,
     *         "like_count": 320
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 150,
     *       "last_page": 8
     *     }
     *   }
     * })
     */
    public function plaza(Request $request)
    {
        try {
            $rules = [
                'category' => 'sometimes|string|in:story,image,voice,video,music,sound,mixed',
                'sort' => 'sometimes|string|in:latest,popular,trending,featured',
                'tags' => 'sometimes|string',
                'search' => 'sometimes|string|max:100',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:50'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'category' => $request->get('category'),
                'sort' => $request->get('sort', 'latest'),
                'tags' => $request->get('tags'),
                'search' => $request->get('search'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->publicationService->getPublicationPlaza($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取作品广场失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取作品广场失败', []);
        }
    }

    /**
     * @ApiTitle(获取作品详情)
     * @ApiSummary(获取指定作品的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/publications/{id}/detail)
     * @ApiParams(name="id", type="int", required=true, description="发布ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "publication_id": 123,
     *     "title": "精彩的AI故事",
     *     "description": "详细描述内容...",
     *     "category": "story",
     *     "tags": ["AI", "科幻", "未来"],
     *     "author": {
     *       "user_id": 456,
     *       "username": "创作者123",
     *       "avatar": "https://example.com/avatar.jpg",
     *       "follower_count": 1250
     *     },
     *     "resource_info": {
     *       "resource_id": 789,
     *       "resource_type": "story",
     *       "file_size": "2.5MB",
     *       "duration": null,
     *       "preview_url": "https://example.com/preview.json"
     *     },
     *     "statistics": {
     *       "view_count": 1250,
     *       "like_count": 89,
     *       "comment_count": 23,
     *       "download_count": 45,
     *       "share_count": 12
     *     },
     *     "published_at": "2024-01-01 12:00:00",
     *     "featured": true,
     *     "allow_comments": true,
     *     "allow_download": true,
     *     "user_interaction": {
     *       "liked": false,
     *       "bookmarked": false,
     *       "downloaded": false
     *     }
     *   }
     * })
     */
    public function detail(Request $request, $publicationId)
    {
        try {
            $userId = auth()->id(); // 可能为null（未登录用户）
            $result = $this->publicationService->getPublicationDetail($publicationId, $userId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取作品详情失败', [
                'method' => __METHOD__,
                'user_id' => $userId ?? null,
                'request_data' => ['publicationId' => $publicationId],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取作品详情失败', []);
        }
    }


}
