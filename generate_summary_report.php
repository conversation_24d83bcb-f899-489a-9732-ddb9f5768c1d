<?php

/**
 * 生成try-catch架构检测汇总报告
 * 专门列出所有不符合要求的方法
 */

// 读取检测报告
$reportFile = 'try_catch_report.json';
if (!file_exists($reportFile)) {
    die("报告文件不存在: $reportFile\n");
}

$reportData = json_decode(file_get_contents($reportFile), true);
if (!$reportData) {
    die("无法解析报告文件\n");
}

// 统计信息
$totalControllers = count($reportData['controllers']);
$totalMethods = 0;
$methodsWithoutTryCatch = [];
$methodsWithInvalidCatch = [];
$controllersWithoutLogImport = [];

echo "=== Try-Catch架构检测汇总报告 ===\n\n";
echo "检测目录: D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api\n";
echo "总控制器数量: $totalControllers\n";
echo "总方法数量: {$reportData['summary']['total_methods']}\n\n";

// 遍历所有控制器
foreach ($reportData['controllers'] as $controller) {
    $controllerName = $controller['file'];
    
    // 检查Log导入
    if (!$controller['has_log_import']) {
        $controllersWithoutLogImport[] = $controllerName;
    }
    
    // 检查每个方法
    foreach ($controller['methods'] as $method) {
        $totalMethods++;
        
        // 没有try-catch的方法
        if (!$method['has_try_catch']) {
            $methodsWithoutTryCatch[] = [
                'controller' => $controllerName,
                'method' => $method['name'],
                'line' => $method['line_number']
            ];
        }
        
        // catch块不符合标准的方法
        if ($method['has_try_catch'] && !$method['valid_catch_block']) {
            $methodsWithInvalidCatch[] = [
                'controller' => $controllerName,
                'method' => $method['name'],
                'line' => $method['line_number'],
                'issues' => $method['catch_issues']
            ];
        }
    }
}

// 输出统计结果
echo "=== 检测结果统计 ===\n";
echo "1. 缺少Log导入的控制器: " . count($controllersWithoutLogImport) . "个\n";
echo "2. 缺少try-catch的方法: " . count($methodsWithoutTryCatch) . "个\n";
echo "3. catch块不符合标准的方法: " . count($methodsWithInvalidCatch) . "个\n\n";

// 详细列出缺少Log导入的控制器
if (!empty($controllersWithoutLogImport)) {
    echo "=== 缺少Log导入的控制器 ===\n";
    foreach ($controllersWithoutLogImport as $controller) {
        echo "- $controller\n";
    }
    echo "\n";
} else {
    echo "✓ 所有控制器都正确导入了Log\n\n";
}

// 详细列出缺少try-catch的方法
if (!empty($methodsWithoutTryCatch)) {
    echo "=== 缺少try-catch架构的方法 ===\n";
    $currentController = '';
    foreach ($methodsWithoutTryCatch as $item) {
        if ($currentController !== $item['controller']) {
            $currentController = $item['controller'];
            echo "\n【{$item['controller']}】\n";
        }
        echo "  - {$item['method']}() (第{$item['line']}行)\n";
    }
    echo "\n";
} else {
    echo "✓ 所有方法都有try-catch架构\n\n";
}

// 详细列出catch块不符合标准的方法
if (!empty($methodsWithInvalidCatch)) {
    echo "=== catch块不符合标准的方法 ===\n";
    $currentController = '';
    foreach ($methodsWithInvalidCatch as $item) {
        if ($currentController !== $item['controller']) {
            $currentController = $item['controller'];
            echo "\n【{$item['controller']}】\n";
        }
        echo "  - {$item['method']}() (第{$item['line']}行)\n";
        if (!empty($item['issues'])) {
            foreach ($item['issues'] as $issue) {
                echo "    * $issue\n";
            }
        }
    }
    echo "\n";
}

// 生成简化的汇总文件
$summaryData = [
    'detection_time' => date('Y-m-d H:i:s'),
    'total_controllers' => $totalControllers,
    'total_methods' => $totalMethods,
    'controllers_without_log_import' => count($controllersWithoutLogImport),
    'methods_without_try_catch' => count($methodsWithoutTryCatch),
    'methods_with_invalid_catch' => count($methodsWithInvalidCatch),
    'controllers_without_log_import_list' => $controllersWithoutLogImport,
    'methods_without_try_catch_list' => $methodsWithoutTryCatch,
    'methods_with_invalid_catch_count_by_controller' => []
];

// 按控制器统计catch块问题
$catchIssuesByController = [];
foreach ($methodsWithInvalidCatch as $item) {
    $controller = $item['controller'];
    if (!isset($catchIssuesByController[$controller])) {
        $catchIssuesByController[$controller] = 0;
    }
    $catchIssuesByController[$controller]++;
}
$summaryData['methods_with_invalid_catch_count_by_controller'] = $catchIssuesByController;

file_put_contents('try_catch_summary.json', json_encode($summaryData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "=== 总结 ===\n";
echo "检测完成时间: " . date('Y-m-d H:i:s') . "\n";
echo "详细报告已保存到: try_catch_report.json\n";
echo "汇总报告已保存到: try_catch_summary.json\n";

if (count($methodsWithoutTryCatch) > 0 || count($methodsWithInvalidCatch) > 0) {
    echo "\n⚠️  发现问题需要修复！\n";
} else {
    echo "\n✅ 所有方法都符合try-catch架构要求！\n";
}

?>