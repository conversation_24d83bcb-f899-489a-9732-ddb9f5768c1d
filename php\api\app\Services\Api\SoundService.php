<?php

namespace App\Services\Api;

use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * 音效生成服务
 * 第2D3阶段：音效生成模块
 */
class SoundService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    /**
     * 生成音效
     */
    public function generateSound(int $userId, string $prompt, ?int $projectId = null, array $generationParams = []): array
    {
        try {
            DB::beginTransaction();

            // 获取模型配置
            $platform = $generationParams['platform'] ?? 'volcengine';
            $model = $this->aiModelService->getModelByPlatform($platform, AiModelConfig::TYPE_SOUND_GENERATION);

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的音效生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '音效生成服务当前不可用',
                    'data' => []
                ];
            }

            // 构建增强提示词
            $enhancedPrompt = $this->buildSoundPrompt($prompt, $generationParams);

            // 计算预估成本
            $estimatedCost = $this->calculateSoundCost($model, $generationParams);

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'sound_generation',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => AiGenerationTask::TYPE_SOUND_GENERATION,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'enhanced_prompt' => $enhancedPrompt,
                    'category' => $generationParams['category'] ?? 'environment',
                    'duration' => $generationParams['duration'] ?? 5,
                    'volume' => $generationParams['volume'] ?? 0.8,
                    'loop' => $generationParams['loop'] ?? false,
                    'fade_in' => $generationParams['fade_in'] ?? 0,
                    'fade_out' => $generationParams['fade_out'] ?? 0
                ],
                'generation_params' => $generationParams,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行生成任务
            $this->executeSoundGeneration($task);

            Log::info('音效生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音效生成任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $estimatedCost,
                    'platform' => $platform
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('音效生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音效生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取音效生成状态
     */
    public function getSoundStatus(int $taskId, int $userId): array
    {
        $task = AiGenerationTask::where('id', $taskId)
            ->where('user_id', $userId)
            ->where('task_type', AiGenerationTask::TYPE_SOUND_GENERATION)
            ->first();

        if (!$task) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '任务不存在',
                'data' => []
            ];
        }

        $data = [
            'id' => $task->id,
            'task_type' => $task->task_type,
            'status' => $task->status,
            'platform' => $task->platform,
            'cost' => $task->cost,
            'processing_time_ms' => $task->processing_time_ms,
            'created_at' => $task->created_at->format('Y-m-d H:i:s'),
            'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
        ];

        // 如果任务完成，添加生成结果
        if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
            $data['audio_url'] = $task->output_data['audio_url'] ?? '';
            $data['duration'] = $task->output_data['duration'] ?? 0;
            $data['file_size'] = $task->output_data['file_size'] ?? '';
        }

        // 如果任务失败，添加错误信息
        if ($task->status === AiGenerationTask::STATUS_FAILED) {
            $data['error_message'] = $task->error_message;
        }

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => $data
        ];
    }

    /**
     * 获取音效生成结果
     */
    public function getSoundResult(int $taskId, int $userId): array
    {
        $task = AiGenerationTask::where('id', $taskId)
            ->where('user_id', $userId)
            ->where('task_type', AiGenerationTask::TYPE_SOUND_GENERATION)
            ->first();

        if (!$task) {
            return [
                'code' => ApiCodeEnum::NOT_FOUND,
                'message' => '任务不存在',
                'data' => []
            ];
        }

        if ($task->status !== AiGenerationTask::STATUS_COMPLETED) {
            return [
                'code' => ApiCodeEnum::FAIL,
                'message' => '任务尚未完成',
                'data' => []
            ];
        }

        $data = [
            'task_id' => $task->id,
            'audio_url' => $task->output_data['audio_url'] ?? '',
            'waveform_url' => $task->output_data['waveform_url'] ?? '',
            'metadata' => [
                'duration' => $task->output_data['duration'] ?? 0,
                'format' => 'wav',
                'sample_rate' => $task->output_data['sample_rate'] ?? '44100Hz',
                'file_size' => $task->output_data['file_size'] ?? '',
                'category' => $task->input_data['category'] ?? '',
                'volume' => $task->input_data['volume'] ?? 0.8,
                'loop' => $task->input_data['loop'] ?? false
            ],
            'download_info' => [
                'direct_url' => $task->output_data['audio_url'] ?? '',
                'expires_at' => Carbon::now()->addDays(7)->format('Y-m-d H:i:s')
            ]
        ];

        return [
            'code' => ApiCodeEnum::SUCCESS,
            'message' => 'success',
            'data' => $data
        ];
    }

    /**
     * 批量生成音效
     */
    public function batchGenerateSounds(int $userId, array $prompts, ?int $projectId = null, array $commonParams = []): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $taskIds = [];
            $totalCost = 0;

            foreach ($prompts as $prompt) {
                $result = $this->generateSound($userId, $prompt, $projectId, $commonParams);
                
                if ($result['code'] === ApiCodeEnum::SUCCESS) {
                    $taskIds[] = $result['data']['task_id'];
                    $totalCost += $result['data']['estimated_cost'];
                } else {
                    DB::rollBack();
                    return $result;
                }
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量音效生成任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'task_ids' => $taskIds,
                    'total_count' => count($taskIds),
                    'estimated_cost' => number_format($totalCost, 4)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('批量音效生成失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量音效生成失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 构建音效提示词
     */
    private function buildSoundPrompt(string $prompt, array $params): string
    {
        $enhancedPrompt = $prompt;

        // 添加分类信息
        if (!empty($params['category'])) {
            $categoryMap = [
                'environment' => '环境音效',
                'action' => '动作音效',
                'ui' => '界面音效',
                'ambient' => '环境氛围',
                'nature' => '自然音效',
                'mechanical' => '机械音效',
                'human' => '人声音效',
                'animal' => '动物音效'
            ];
            $enhancedPrompt .= "\n\n音效分类：" . ($categoryMap[$params['category']] ?? $params['category']);
        }

        // 添加时长要求
        $duration = $params['duration'] ?? 5;
        $enhancedPrompt .= "\n音效时长：{$duration}秒";

        // 添加音量要求
        $volume = $params['volume'] ?? 0.8;
        $enhancedPrompt .= "\n音量级别：" . ($volume * 100) . "%";

        // 添加循环要求
        if (!empty($params['loop']) && $params['loop']) {
            $enhancedPrompt .= "\n循环播放：是";
        }

        return $enhancedPrompt;
    }

    /**
     * 计算音效生成成本
     */
    private function calculateSoundCost(AiModelConfig $model, array $params): float
    {
        $baseCost = $model->cost_per_token;
        
        // 时长影响成本
        $duration = $params['duration'] ?? 5;
        $durationMultiplier = $duration / 5; // 以5秒为基准
        
        // 复杂度影响成本
        $complexity = 1.0;
        $category = $params['category'] ?? 'environment';
        if (in_array($category, ['action', 'mechanical'])) {
            $complexity = 1.3; // 复杂音效增加成本
        }

        return round($baseCost * $durationMultiplier * $complexity, 4);
    }

    /**
     * 执行音效生成
     */
    private function executeSoundGeneration(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 调用AI服务
            $result = $this->callAiService($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'sound_generation', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'sound_generation_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'sound_generation_error', $task->id);

            Log::error('音效生成执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务
     */
    private function callAiService(AiGenerationTask $task): array
    {
        try {
            $aiApiUrl = config('ai.api_url', 'https://aiapi.tiptop.cn');
            $platformConfig = config("ai.platforms.{$task->platform}");
            $endpoint = $platformConfig['endpoint'] ?? '/volcengine/bigmodel/sound_generation';

            $timeout = $platformConfig['timeout'] ?? config('ai.timeout', 60);
            $response = Http::timeout($timeout)->post($aiApiUrl . $endpoint, [
                'prompt' => $task->input_data['enhanced_prompt'],
                'category' => $task->input_data['category'],
                'duration' => $task->input_data['duration'],
                'volume' => $task->input_data['volume'],
                'loop' => $task->input_data['loop'],
                'fade_in' => $task->input_data['fade_in'],
                'fade_out' => $task->input_data['fade_out']
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'success' => true,
                    'data' => [
                        'audio_url' => $data['data']['audio_url'] ?? '',
                        'waveform_url' => $data['data']['waveform_url'] ?? '',
                        'duration' => $data['data']['duration'] ?? $task->input_data['duration'],
                        'file_size' => $data['data']['file_size'] ?? '0.8MB',
                        'sample_rate' => $data['data']['sample_rate'] ?? '44100Hz'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage()
            ];
        }
    }
}
