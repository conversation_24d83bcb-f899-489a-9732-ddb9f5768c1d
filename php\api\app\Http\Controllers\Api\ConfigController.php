<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\ConfigService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 系统配置控制器
 * 处理系统参数配置、功能开关、AI平台配置等
 */
class ConfigController extends Controller
{
    protected $configService;

    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * @ApiTitle(获取系统配置列表)
     * @ApiSummary(查询系统配置参数)
     * @ApiMethod(GET)
     * @ApiRoute(/api/config)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="category", type="string", required=false, description="配置分类：system,ai,payment,feature")
     * @ApiParams(name="key", type="string", required=false, description="配置键名搜索")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "configs": [
     *       {
     *         "id": 1,
     *         "key": "ai.default_platform",
     *         "value": "liblib",
     *         "category": "ai",
     *         "description": "默认AI平台",
     *         "type": "string",
     *         "options": ["liblib", "deepseek", "kling", "minimax"],
     *         "updated_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "categories": ["system", "ai", "payment", "feature"],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 50,
     *       "total": 25,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function index(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看系统配置');
            }

            $rules = [
                'category' => 'sometimes|string|in:system,ai,payment,feature,security',
                'key' => 'sometimes|string|max:100',
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'category' => $request->get('category'),
                'key' => $request->get('key'),
                'page' => $request->get('page', 1),
                'per_page' => 50
            ];

            $result = $this->configService->getConfigs($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取系统配置列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取系统配置列表失败', []);
        }
    }

    /**
     * @ApiTitle(获取公开配置)
     * @ApiSummary(获取前端可访问的公开配置)
     * @ApiMethod(GET)
     * @ApiRoute(/api/config/public)
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "app_name": "TipTop AI创作平台",
     *     "app_version": "1.0.0",
     *     "features": {
     *       "image_generation": true,
     *       "video_generation": true,
     *       "voice_synthesis": true,
     *       "music_generation": false
     *     },
     *     "limits": {
     *       "max_file_size": 10485760,
     *       "max_generation_time": 300,
     *       "daily_free_quota": 10
     *     },
     *     "ai_platforms": {
     *       "available": ["liblib", "deepseek", "kling", "minimax"],
     *       "default": "liblib"
     *     }
     *   }
     * })
     */
    public function getPublicConfig()
    {
        try {
            $result = $this->configService->getPublicConfig();

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取公开配置失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => [],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取公开配置失败', []);
        }
    }

    /**
     * @ApiTitle(更新系统配置)
     * @ApiSummary(更新指定的系统配置)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/config/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="配置ID")
     * @ApiParams(name="value", type="mixed", required=true, description="配置值")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "配置更新成功",
     *   "data": {
     *     "id": 1,
     *     "key": "ai.default_platform",
     *     "value": "deepseek",
     *     "old_value": "liblib",
     *     "updated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function update($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可修改系统配置');
            }

            $rules = [
                'value' => 'required'
            ];

            $messages = [
                'value.required' => '配置值不能为空'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $this->configService->updateConfig($id, $request->value, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('更新系统配置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新系统配置失败', []);
        }
    }

    /**
     * @ApiTitle(批量更新配置)
     * @ApiSummary(批量更新多个系统配置)
     * @ApiMethod(PUT)
     * @ApiRoute(/api/config/batch)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="configs", type="array", required=true, description="配置数组")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "配置批量更新成功",
     *   "data": {
     *     "updated_count": 5,
     *     "failed_count": 0,
     *     "updated_configs": [
     *       {
     *         "id": 1,
     *         "key": "ai.default_platform",
     *         "value": "deepseek"
     *       }
     *     ]
     *   }
     * })
     */
    public function batchUpdate(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可修改系统配置');
            }

            $rules = [
                'configs' => 'required|array|min:1|max:50',
                'configs.*.id' => 'required|integer|exists:configs,id',
                'configs.*.value' => 'required'
            ];

            $messages = [
                'configs.required' => '配置数组不能为空',
                'configs.array' => '配置必须是数组格式',
                'configs.min' => '至少需要1个配置',
                'configs.max' => '最多支持50个配置',
                'configs.*.id.required' => '配置ID不能为空',
                'configs.*.id.exists' => '配置不存在',
                'configs.*.value.required' => '配置值不能为空'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $this->configService->batchUpdateConfigs($request->configs, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量更新配置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量更新配置失败', []);
        }
    }

    /**
     * @ApiTitle(重置配置为默认值)
     * @ApiSummary(重置指定配置为系统默认值)
     * @ApiMethod(POST)
     * @ApiRoute(/api/config/{id}/reset)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="配置ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "配置重置成功",
     *   "data": {
     *     "id": 1,
     *     "key": "ai.default_platform",
     *     "value": "liblib",
     *     "old_value": "deepseek",
     *     "reset_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function reset($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可重置系统配置');
            }

            $result = $this->configService->resetConfig($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('重置配置为默认值失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '重置配置为默认值失败', []);
        }
    }

    /**
     * @ApiTitle(获取配置历史)
     * @ApiSummary(查询配置的修改历史记录)
     * @ApiMethod(GET)
     * @ApiRoute(/api/config/{id}/history)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="配置ID")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "config": {
     *       "id": 1,
     *       "key": "ai.default_platform",
     *       "current_value": "deepseek"
     *     },
     *     "history": [
     *       {
     *         "id": 1,
     *         "old_value": "liblib",
     *         "new_value": "deepseek",
     *         "operator": "admin",
     *         "operator_id": 1,
     *         "created_at": "2024-01-01 12:00:00",
     *         "reason": "切换默认AI平台"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 5,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function history($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看配置历史');
            }

            $rules = [
                'page' => 'sometimes|integer|min:1'
            ];

            $this->validateData($request->all(), $rules);

            $page = $request->get('page', 1, []);

            $result = $this->configService->getConfigHistory($id, $page);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取配置历史失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取配置历史失败', []);
        }
    }

    /**
     * @ApiTitle(验证配置值)
     * @ApiSummary(验证配置值是否有效)
     * @ApiMethod(POST)
     * @ApiRoute(/api/config/validate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="key", type="string", required=true, description="配置键名")
     * @ApiParams(name="value", type="mixed", required=true, description="配置值")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "配置值验证通过",
     *   "data": {
     *     "valid": true,
     *     "formatted_value": "deepseek",
     *     "warnings": []
     *   }
     * })
     */
    public function validateConfig(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可验证配置');
            }

            $rules = [
                'key' => 'required|string|max:100',
                'value' => 'required'
            ];

            $messages = [
                'key.required' => '配置键名不能为空',
                'key.max' => '配置键名不能超过100个字符',
                'value.required' => '配置值不能为空'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $result = $this->configService->validateConfigValue($request->key, $request->value);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('验证配置值失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '验证配置值失败', []);
        }
    }
}
