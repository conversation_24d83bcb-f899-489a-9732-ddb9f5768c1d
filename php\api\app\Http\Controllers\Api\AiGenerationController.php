<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\AiGenerationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * AI内容生成与任务管理-向第三方AI平台（虚拟AI API服务）发起请求
 */
class AiGenerationController extends Controller
{
    protected $aiGenerationService;

    public function __construct(AiGenerationService $aiGenerationService)
    {
        $this->aiGenerationService = $aiGenerationService;
    }

    /**
     * @ApiTitle(文本生成)
     * @ApiSummary(使用AI生成文本内容)
     * @ApiMethod(POST)
     * @ApiRoute(/api/ai/text/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="生成提示词")
     * @ApiParams(name="model_id", type="int", required=false, description="指定模型ID")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="max_tokens", type="int", required=false, description="最大token数")
     * @ApiParams(name="temperature", type="float", required=false, description="温度参数")
     * @ApiParams(name="top_p", type="float", required=false, description="Top-p参数")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.generated_text", type="string", required=false, description="生成的文本")
     * @ApiReturnParams (name="data.tokens_used", type="int", required=false, description="使用的token数")
     * @ApiReturnParams (name="data.cost", type="decimal", required=false, description="消耗的积分")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "文本生成成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "completed",
     *     "generated_text": "这是AI生成的文本内容...",
     *     "tokens_used": 150,
     *     "cost": "0.0150"
     *   }
     * })
     */
    public function generateText(Request $request)
    {
        try {
            $rules = [
                'prompt' => 'required|string|min:1|max:4000',
                'model_id' => 'sometimes|integer|exists:ai_model_configs,id',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'max_tokens' => 'sometimes|integer|min:1|max:4000',
                'temperature' => 'sometimes|numeric|min:0|max:2',
                'top_p' => 'sometimes|numeric|min:0|max:1'
            ];

            $messages = [
                'prompt.required' => '提示词不能为空',
                'prompt.min' => '提示词不能为空',
                'prompt.max' => '提示词不能超过4000个字符',
                'model_id.exists' => '模型不存在',
                'project_id.exists' => '项目不存在',
                'max_tokens.min' => 'token数不能少于1',
                'max_tokens.max' => 'token数不能超过4000',
                'temperature.min' => '温度参数不能小于0',
                'temperature.max' => '温度参数不能大于2',
                'top_p.min' => 'Top-p参数不能小于0',
                'top_p.max' => 'Top-p参数不能大于1'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];

            $generationParams = [
                'max_tokens' => $request->get('max_tokens', 1000),
                'temperature' => $request->get('temperature', 0.7),
                'top_p' => $request->get('top_p', 0.9)
            ];

            $result = $this->aiGenerationService->generateText(
                $user->id,
                $request->prompt,
                $request->model_id,
                $request->project_id,
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('AI文本生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, 'AI文本生成失败', []);
        }
    }

    /**
     * @ApiTitle(获取生成任务状态)
     * @ApiSummary(查询AI生成任务的状态和结果)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "text_generation",
     *     "status": "completed",
     *     "platform": "deepseek",
     *     "model_name": "deepseek-chat",
     *     "input_data": {
     *       "prompt": "生成一个故事"
     *     },
     *     "output_data": {
     *       "text": "这是生成的故事..."
     *     },
     *     "cost": "0.0150",
     *     "tokens_used": 150,
     *     "processing_time_ms": 1200,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:01"
     *   }
     * })
     */
    public function getTaskStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];
            $result = $this->aiGenerationService->getTaskStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取AI任务状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取AI任务状态失败', []);
        }
    }

    /**
     * @ApiTitle(获取用户生成任务列表)
     * @ApiSummary(获取用户的AI生成任务历史)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="task_type", type="string", required=false, description="任务类型筛选")
     * @ApiParams(name="status", type="string", required=false, description="状态筛选")
     * @ApiParams(name="platform", type="string", required=false, description="平台筛选")
     * @ApiParams(name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量，默认20")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "tasks": [
     *       {
     *         "id": 123,
     *         "task_type": "text_generation",
     *         "status": "completed",
     *         "platform": "deepseek",
     *         "cost": "0.0150",
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "total": 50,
     *       "per_page": 20
     *     }
     *   }
     * })
     */
    public function getUserTasks(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];
            $page = $request->get('page', 1);
            $perPage = min($request->get('per_page', 20), 100);

            $filters = [
                'task_type' => $request->get('task_type'),
                'status' => $request->get('status'),
                'platform' => $request->get('platform')
            ];

            $result = $this->aiGenerationService->getUserTasks(
                $user->id,
                $filters,
                $page,
                $perPage
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取用户AI任务列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取用户AI任务列表失败', []);
        }
    }

    /**
     * @ApiTitle(重试失败的任务)
     * @ApiSummary(重新执行失败的AI生成任务)
     * @ApiMethod(POST)
     * @ApiRoute(/api/ai/tasks/{id}/retry)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "任务重试成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "retry_count": 1
     *   }
     * })
     */
    public function retryTask(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(
                    $authResult['response']['code'],
                    $authResult['response']['message'],
                    $authResult['response']['data']
                );
            }

            $user = $authResult['user'];
            $result = $this->aiGenerationService->retryTask($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('重试AI任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '重试AI任务失败', []);
        }
    }
}
