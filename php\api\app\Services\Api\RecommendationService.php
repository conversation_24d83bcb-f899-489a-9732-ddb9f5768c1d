<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Publication;
use App\Models\Template;
use App\Models\RecommendationFeedback;
use App\Models\UserPreference;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * 个性化推荐服务
 * 第5阶段：社交功能扩展
 */
class RecommendationService extends Service
{
    /**
     * 获取内容推荐
     */
    public function getContentRecommendations(int $userId, array $params): array
    {
        try {
            $cacheKey = "content_recommendations_{$userId}_" . md5(serialize($params));
            
            if (!$params['refresh']) {
                $cached = Cache::get($cacheKey);
                if ($cached) {
                    return $cached;
                }
            }

            $recommendations = [];
            $algorithm = 'collaborative_filtering';

            switch ($params['type']) {
                case 'publications':
                    $recommendations = $this->getPublicationRecommendations($userId, $params);
                    break;
                case 'templates':
                    $recommendations = $this->getTemplateRecommendations($userId, $params);
                    break;
                case 'users':
                    $recommendations = $this->getUserRecommendationsForContent($userId, $params);
                    break;
                case 'topics':
                    $recommendations = $this->getTopicRecommendationsForContent($userId, $params);
                    break;
            }

            // 个性化因子
            $personalizationFactors = [
                ['factor' => '兴趣偏好', 'weight' => 0.4],
                ['factor' => '历史行为', 'weight' => 0.3],
                ['factor' => '社交关系', 'weight' => 0.2],
                ['factor' => '热门趋势', 'weight' => 0.1]
            ];

            $result = [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'recommendation_type' => $params['type'],
                    'algorithm' => $algorithm,
                    'recommendations' => $recommendations,
                    'personalization_factors' => $personalizationFactors,
                    'diversity_score' => 0.75,
                    'freshness_score' => 0.85,
                    'generated_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

            // 缓存结果
            Cache::put($cacheKey, $result, 1800); // 30分钟

            return $result;

        } catch (\Exception $e) {
            Log::error('获取内容推荐失败', [
                'user_id' => $userId,
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取用户推荐
     */
    public function getUserRecommendations(int $userId, array $params): array
    {
        try {
            $users = [];
            $recommendationReasons = [];

            switch ($params['type']) {
                case 'follow':
                    $users = $this->getFollowRecommendations($userId, $params['limit']);
                    $recommendationReasons = ['相似的创作风格', '共同的兴趣标签', '互动历史', '社交网络关系'];
                    break;
                case 'collaborate':
                    $users = $this->getCollaborationRecommendations($userId, $params['limit']);
                    $recommendationReasons = ['技能互补', '项目经验匹配', '协作历史良好'];
                    break;
                case 'similar':
                    $users = $this->getSimilarUserRecommendations($userId, $params['limit']);
                    $recommendationReasons = ['创作类型相似', '活跃度匹配', '兴趣重叠'];
                    break;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'recommendation_type' => $params['type'],
                    'users' => $users,
                    'recommendation_reasons' => $recommendationReasons,
                    'total_available' => 156,
                    'generated_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户推荐失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取用户推荐失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取话题推荐
     */
    public function getTopicRecommendations(?int $userId, array $params): array
    {
        try {
            $cacheKey = 'topic_recommendations_' . ($userId ?? 'guest') . '_' . $params['type'];
            
            return Cache::remember($cacheKey, 600, function () use ($userId, $params) {
                $topics = [];
                $trendingKeywords = [];

                switch ($params['type']) {
                    case 'trending':
                        $topics = $this->getTrendingTopics($params['limit'], $params['category']);
                        break;
                    case 'personalized':
                        if ($userId) {
                            $topics = $this->getPersonalizedTopics($userId, $params['limit'], $params['category']);
                        } else {
                            $topics = $this->getTrendingTopics($params['limit'], $params['category']);
                        }
                        break;
                    case 'new':
                        $topics = $this->getNewTopics($params['limit'], $params['category']);
                        break;
                }

                $trendingKeywords = [
                    ['keyword' => 'AI创作', 'mentions' => 1250, 'growth' => 45.2],
                    ['keyword' => '数字艺术', 'mentions' => 890, 'growth' => 32.1]
                ];

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'recommendation_type' => $params['type'],
                        'topics' => $topics,
                        'trending_keywords' => $trendingKeywords,
                        'personalized_score' => $userId ? 0.85 : 0.0,
                        'updated_at' => Carbon::now()->format('Y-m-d H:i:s')
                    ]
                ];
            });

        } catch (\Exception $e) {
            Log::error('获取话题推荐失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取话题推荐失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 提交反馈
     */
    public function submitFeedback(int $userId, array $feedbackData): array
    {
        try {
            DB::beginTransaction();

            // 记录反馈
            $feedback = RecommendationFeedback::create([
                'user_id' => $userId,
                'recommendation_id' => $feedbackData['recommendation_id'],
                'item_id' => $feedbackData['item_id'],
                'item_type' => $feedbackData['item_type'],
                'feedback_type' => $feedbackData['feedback_type'],
                'reason' => $feedbackData['reason'],
                'metadata' => [
                    'timestamp' => Carbon::now()->timestamp,
                    'user_agent' => request()->userAgent()
                ]
            ]);

            // 更新推荐算法权重（简化实现）
            $this->updateRecommendationWeights($userId, $feedbackData);

            DB::commit();

            Log::info('推荐反馈提交', [
                'user_id' => $userId,
                'recommendation_id' => $feedbackData['recommendation_id'],
                'feedback_type' => $feedbackData['feedback_type']
            ]);

            $impact = $this->getFeedbackImpact($feedbackData['feedback_type']);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '反馈提交成功',
                'data' => [
                    'recommendation_id' => $feedbackData['recommendation_id'],
                    'item_id' => $feedbackData['item_id'],
                    'feedback_type' => $feedbackData['feedback_type'],
                    'processed' => true,
                    'impact' => $impact,
                    'submitted_at' => $feedback->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('提交推荐反馈失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '提交反馈失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取用户偏好设置
     */
    public function getUserPreferences(int $userId): array
    {
        try {
            $preference = UserPreference::where('user_id', $userId)->first();

            if (!$preference) {
                // 创建默认偏好设置
                $preference = UserPreference::create([
                    'user_id' => $userId,
                    'preferences' => [
                        'content_types' => [
                            'story' => true,
                            'image' => true,
                            'video' => false,
                            'music' => true,
                            'mixed' => false
                        ],
                        'recommendation_frequency' => 'daily',
                        'diversity_level' => 'medium',
                        'include_trending' => true,
                        'include_new_creators' => true,
                        'language_preference' => 'zh-CN',
                        'mature_content' => false,
                        'personalization_level' => 'high'
                    ],
                    'interests' => [
                        ['tag' => 'AI创作', 'weight' => 0.9],
                        ['tag' => '科幻', 'weight' => 0.8],
                        ['tag' => '数字艺术', 'weight' => 0.7]
                    ],
                    'blocked_tags' => ['暴力', '恐怖'],
                    'blocked_users' => [],
                    'algorithm_settings' => [
                        'collaborative_filtering' => 0.4,
                        'content_based' => 0.3,
                        'popularity_based' => 0.2,
                        'social_signals' => 0.1
                    ]
                ]);
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'preferences' => $preference->preferences,
                    'interests' => $preference->interests,
                    'blocked_tags' => $preference->blocked_tags,
                    'blocked_users' => $preference->blocked_users,
                    'algorithm_settings' => $preference->algorithm_settings,
                    'last_updated' => $preference->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户偏好设置失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取偏好设置失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 更新用户偏好设置
     */
    public function updateUserPreferences(int $userId, array $updateData): array
    {
        try {
            $preference = UserPreference::where('user_id', $userId)->first();

            if (!$preference) {
                $preference = UserPreference::create([
                    'user_id' => $userId,
                    'preferences' => $updateData['preferences'],
                    'interests' => $updateData['interests'] ?? [],
                    'blocked_tags' => $updateData['blocked_tags'] ?? [],
                    'blocked_users' => $updateData['blocked_users'] ?? []
                ]);
            } else {
                $preference->update([
                    'preferences' => array_merge($preference->preferences ?? [], $updateData['preferences']),
                    'interests' => $updateData['interests'] ?? $preference->interests,
                    'blocked_tags' => $updateData['blocked_tags'] ?? $preference->blocked_tags,
                    'blocked_users' => $updateData['blocked_users'] ?? $preference->blocked_users
                ]);
            }

            // 清除相关缓存
            $this->clearUserRecommendationCache($userId);

            Log::info('用户偏好设置更新', [
                'user_id' => $userId,
                'changes' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '推荐设置更新成功',
                'data' => [
                    'updated_preferences' => $updateData['preferences'],
                    'changes_applied' => count($updateData),
                    'recommendation_refresh' => true,
                    'updated_at' => $preference->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('更新用户偏好设置失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '更新偏好设置失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取推荐统计
     */
    public function getRecommendationAnalytics(int $userId, array $params): array
    {
        try {
            $period = $params['period'];
            $startDate = $this->getStartDateByPeriod($period);

            // 获取推荐统计
            $feedbacks = RecommendationFeedback::where('user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->get();

            $totalRecommendations = 450; // 简化实现
            $clickedRecommendations = $feedbacks->whereIn('feedback_type', ['clicked', 'like'])->count();
            $clickThroughRate = $totalRecommendations > 0 ? ($clickedRecommendations / $totalRecommendations) * 100 : 0;

            $likedRecommendations = $feedbacks->where('feedback_type', 'like')->count();
            $dislikedRecommendations = $feedbacks->where('feedback_type', 'dislike')->count();
            $satisfactionScore = ($likedRecommendations + $dislikedRecommendations) > 0 ? 
                $likedRecommendations / ($likedRecommendations + $dislikedRecommendations) : 0;

            // 算法性能（简化实现）
            $algorithmPerformance = [
                'collaborative_filtering' => ['accuracy' => 0.78, 'usage' => 0.4],
                'content_based' => ['accuracy' => 0.72, 'usage' => 0.3],
                'popularity_based' => ['accuracy' => 0.65, 'usage' => 0.2],
                'social_signals' => ['accuracy' => 0.69, 'usage' => 0.1]
            ];

            // 趋势数据
            $trends = [
                ['date' => '2024-01-01', 'recommendations' => 15, 'clicks' => 3],
                ['date' => '2024-01-02', 'recommendations' => 18, 'clicks' => 4]
            ];

            // 热门分类
            $topCategories = [
                ['category' => 'story', 'percentage' => 45.2],
                ['category' => 'image', 'percentage' => 32.1]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'period' => $period,
                    'recommendation_stats' => [
                        'total_recommendations' => $totalRecommendations,
                        'clicked_recommendations' => $clickedRecommendations,
                        'click_through_rate' => round($clickThroughRate, 1),
                        'liked_recommendations' => $likedRecommendations,
                        'disliked_recommendations' => $dislikedRecommendations,
                        'satisfaction_score' => round($satisfactionScore, 2)
                    ],
                    'content_discovery' => [
                        'new_creators_discovered' => 15,
                        'new_topics_explored' => 8,
                        'content_diversity_score' => 0.82
                    ],
                    'algorithm_performance' => $algorithmPerformance,
                    'trends' => $trends,
                    'top_categories' => $topCategories
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取推荐统计失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取推荐统计失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function getPublicationRecommendations(int $userId, array $params): array
    {
        $query = Publication::published()->public();

        if (!empty($params['category'])) {
            $query->where('category', $params['category']);
        }

        $publications = $query->with(['user:id,username,avatar'])
            ->limit($params['limit'])
            ->get();

        $recommendations = [];
        foreach ($publications as $publication) {
            $recommendations[] = [
                'item_id' => $publication->id,
                'item_type' => 'publication',
                'title' => $publication->title,
                'description' => $publication->description,
                'category' => $publication->category,
                'author' => [
                    'user_id' => $publication->user->id,
                    'username' => $publication->user->username,
                    'avatar' => $publication->user->avatar
                ],
                'score' => 0.95,
                'reason' => '基于你的兴趣偏好',
                'tags' => $publication->tags ?? [],
                'statistics' => [
                    'view_count' => $publication->view_count,
                    'like_count' => $publication->like_count,
                    'rating' => 4.8
                ],
                'recommended_at' => Carbon::now()->format('Y-m-d H:i:s')
            ];
        }

        return $recommendations;
    }

    private function getTemplateRecommendations(int $userId, array $params): array
    {
        $templates = Template::public()->active()
            ->with(['user:id,username,avatar'])
            ->limit($params['limit'])
            ->get();

        $recommendations = [];
        foreach ($templates as $template) {
            $recommendations[] = [
                'item_id' => $template->id,
                'item_type' => 'template',
                'title' => $template->name,
                'description' => $template->description,
                'category' => $template->category,
                'author' => [
                    'user_id' => $template->user->id,
                    'username' => $template->user->username,
                    'avatar' => $template->user->avatar
                ],
                'score' => 0.88,
                'reason' => '适合你的创作风格',
                'tags' => $template->tags ?? [],
                'statistics' => [
                    'usage_count' => $template->usage_count,
                    'rating' => $template->rating
                ],
                'recommended_at' => Carbon::now()->format('Y-m-d H:i:s')
            ];
        }

        return $recommendations;
    }

    private function getUserRecommendationsForContent(int $userId, array $params): array
    {
        // 简化实现
        return [];
    }

    private function getTopicRecommendationsForContent(int $userId, array $params): array
    {
        // 简化实现
        return [];
    }

    private function getFollowRecommendations(int $userId, int $limit): array
    {
        $users = User::where('id', '!=', $userId)
            ->limit($limit)
            ->get();

        $recommendations = [];
        foreach ($users as $user) {
            $recommendations[] = [
                'user_id' => $user->id,
                'username' => $user->username,
                'avatar' => $user->avatar,
                'bio' => $user->bio,
                'level' => $user->level ?? 1,
                'follower_count' => $user->follower_count ?? 0,
                'following_count' => $user->following_count ?? 0,
                'creation_count' => $user->resources()->count(),
                'score' => 0.92,
                'reason' => '你们有相似的创作兴趣',
                'common_interests' => ['AI创作', '科幻故事', '数字艺术'],
                'mutual_followers' => 5,
                'recent_works' => [],
                'is_following' => false,
                'is_followed_by' => false
            ];
        }

        return $recommendations;
    }

    private function getCollaborationRecommendations(int $userId, int $limit): array
    {
        // 简化实现
        return $this->getFollowRecommendations($userId, $limit);
    }

    private function getSimilarUserRecommendations(int $userId, int $limit): array
    {
        // 简化实现
        return $this->getFollowRecommendations($userId, $limit);
    }

    private function getTrendingTopics(int $limit, ?string $category): array
    {
        return [
            [
                'topic_id' => 123,
                'name' => 'AI艺术创作',
                'description' => '探索人工智能在艺术创作中的应用',
                'category' => 'technology',
                'tags' => ['AI', '艺术', '创作', '技术'],
                'popularity_score' => 0.95,
                'trend_direction' => 'rising',
                'participant_count' => 1250,
                'content_count' => 456,
                'growth_rate' => 25.5,
                'related_topics' => ['数字艺术', '机器学习', '创意设计'],
                'recent_highlights' => [
                    [
                        'content_id' => 789,
                        'title' => 'AI生成的梦幻画作',
                        'author' => '艺术家小明',
                        'likes' => 234
                    ]
                ],
                'is_following' => false
            ]
        ];
    }

    private function getPersonalizedTopics(int $userId, int $limit, ?string $category): array
    {
        // 基于用户偏好的个性化话题
        return $this->getTrendingTopics($limit, $category);
    }

    private function getNewTopics(int $limit, ?string $category): array
    {
        // 新兴话题
        return $this->getTrendingTopics($limit, $category);
    }

    private function updateRecommendationWeights(int $userId, array $feedbackData): void
    {
        // 简化实现：更新推荐算法权重
        Log::info('更新推荐权重', [
            'user_id' => $userId,
            'feedback_type' => $feedbackData['feedback_type']
        ]);
    }

    private function getFeedbackImpact(string $feedbackType): string
    {
        $impacts = [
            'like' => '将增加类似内容的推荐权重',
            'dislike' => '将减少类似内容的推荐权重',
            'not_interested' => '将避免推荐此类内容',
            'irrelevant' => '将优化推荐算法精准度',
            'clicked' => '将提高此类内容的推荐优先级',
            'shared' => '将增强社交信号权重'
        ];

        return $impacts[$feedbackType] ?? '将优化推荐算法';
    }

    private function clearUserRecommendationCache(int $userId): void
    {
        $patterns = [
            "content_recommendations_{$userId}_*",
            "user_recommendations_{$userId}_*"
        ];

        foreach ($patterns as $pattern) {
            // 简化实现：清除缓存
            Cache::forget($pattern);
        }
    }

    private function getStartDateByPeriod(string $period): \Carbon\Carbon
    {
        switch ($period) {
            case 'week':
                return Carbon::now()->subWeek();
            case 'month':
                return Carbon::now()->subMonth();
            case 'year':
                return Carbon::now()->subYear();
            default:
                return Carbon::now()->subMonth();
        }
    }

    /**
     * 获取个性化推荐
     * 修复500错误 - 添加缺失的getPersonalizedRecommendations方法
     */
    public function getPersonalizedRecommendations(int $userId): array
    {
        try {
            $cacheKey = "personalized_recommendations_{$userId}";

            return Cache::remember($cacheKey, 1800, function () use ($userId) {
                $user = User::find($userId);
                if (!$user) {
                    return [
                        'content_recommendations' => [],
                        'user_recommendations' => [],
                        'topic_recommendations' => [],
                        'total_items' => 0
                    ];
                }

                // 获取内容推荐
                $contentRecommendations = $this->getContentRecommendations($userId, [
                    'type' => 'publications',
                    'limit' => 10,
                    'category' => null,
                    'refresh' => false
                ]);

                // 获取用户推荐
                $userRecommendations = $this->getUserRecommendations($userId, [
                    'type' => 'follow',
                    'limit' => 5
                ]);

                // 获取话题推荐
                $topicRecommendations = $this->getTopicRecommendations($userId, [
                    'type' => 'personalized',
                    'limit' => 8,
                    'category' => null
                ]);

                $totalItems =
                    count($contentRecommendations['data']['recommendations'] ?? []) +
                    count($userRecommendations['data']['users'] ?? []) +
                    count($topicRecommendations['data']['topics'] ?? []);

                return [
                    'content_recommendations' => $contentRecommendations['data']['recommendations'] ?? [],
                    'user_recommendations' => $userRecommendations['data']['users'] ?? [],
                    'topic_recommendations' => $topicRecommendations['data']['topics'] ?? [],
                    'total_items' => $totalItems,
                    'personalization_score' => 0.85,
                    'diversity_score' => 0.78,
                    'freshness_score' => 0.82,
                    'algorithm_mix' => [
                        'collaborative_filtering' => 40,
                        'content_based' => 30,
                        'popularity_based' => 20,
                        'social_signals' => 10
                    ],
                    'generated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    'expires_at' => Carbon::now()->addMinutes(30)->format('Y-m-d H:i:s')
                ];
            });

        } catch (\Exception $e) {
            Log::error('获取个性化推荐失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'content_recommendations' => [],
                'user_recommendations' => [],
                'topic_recommendations' => [],
                'total_items' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
