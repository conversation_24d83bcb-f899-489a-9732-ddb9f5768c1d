<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\Resource;
use App\Models\ResourceExport;
use App\Models\ResourceVersion;
use App\Models\ResourceDownload;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Http\Response;

/**
 * 下载管理服务
 * 第3A阶段：下载管理系统模块
 */
class DownloadManagementService extends Service
{
    /**
     * 获取下载历史
     */
    public function getDownloadHistory(int $userId, array $filters): array
    {
        try {
            $query = ResourceDownload::where('user_id', $userId);

            // 应用过滤条件
            if (!empty($filters['download_type'])) {
                $query->where('download_type', $filters['download_type']);
            }

            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }

            if (!empty($filters['date_from'])) {
                $query->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->whereDate('created_at', '<=', $filters['date_to']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $downloads = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            // 构建下载列表
            $downloadList = [];
            foreach ($downloads->items() as $download) {
                $downloadList[] = [
                    'download_id' => $download->id,
                    'download_type' => $download->download_type,
                    'target_id' => $download->target_id,
                    'target_name' => $download->target_name,
                    'file_size' => $download->formatted_file_size,
                    'status' => $download->status,
                    'download_url' => $download->isAvailable() ? $download->getDownloadUrl() : null,
                    'downloaded_at' => $download->downloaded_at ? $download->downloaded_at->format('Y-m-d H:i:s') : null,
                    'expires_at' => $download->expires_at ? $download->expires_at->format('Y-m-d H:i:s') : null,
                    'user_agent' => $download->user_agent,
                    'ip_address' => $download->ip_address
                ];
            }

            // 计算统计信息
            $statistics = $this->calculateDownloadStatistics($userId, $filters);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'downloads' => $downloadList,
                    'statistics' => $statistics,
                    'pagination' => [
                        'current_page' => $downloads->currentPage(),
                        'per_page' => $downloads->perPage(),
                        'total' => $downloads->total(),
                        'last_page' => $downloads->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取下载历史失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取下载历史失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 重试下载
     */
    public function retryDownload(int $downloadId, int $userId): array
    {
        try {
            DB::beginTransaction();

            $download = ResourceDownload::where('id', $downloadId)
                ->where('user_id', $userId)
                ->first();

            if (!$download) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '下载记录不存在',
                    'data' => []
                ];
            }

            if ($download->status !== ResourceDownload::STATUS_FAILED) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '只能重试失败的下载任务',
                    'data' => []
                ];
            }

            // 检查重试次数限制
            if ($download->retry_count >= 3) {
                return [
                    'code' => ApiCodeEnum::LIMIT_EXCEEDED,
                    'message' => '重试次数已达上限',
                    'data' => []
                ];
            }

            // 验证目标资源是否仍然可用
            $targetAvailable = $this->checkTargetAvailability($download->download_type, $download->target_id);
            if (!$targetAvailable) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '目标资源不再可用',
                    'data' => []
                ];
            }

            // 生成新的下载令牌
            $newToken = Str::random(64);
            $expiresAt = Carbon::now()->addHours(6); // 6小时有效期

            // 更新下载记录
            $download->update([
                'status' => ResourceDownload::STATUS_READY,
                'download_token' => $newToken,
                'expires_at' => $expiresAt,
                'retry_count' => $download->retry_count + 1,
                'error_message' => null
            ]);

            // 缓存下载令牌信息
            Cache::put(
                'download_token:' . $newToken,
                [
                    'download_id' => $download->id,
                    'user_id' => $userId,
                    'target_type' => $download->download_type,
                    'target_id' => $download->target_id
                ],
                $expiresAt
            );

            DB::commit();

            Log::info('下载重试成功', [
                'download_id' => $downloadId,
                'user_id' => $userId,
                'retry_count' => $download->retry_count
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '下载重试成功',
                'data' => [
                    'download_id' => $download->id,
                    'new_download_url' => route('api.downloads.secure', $newToken),
                    'status' => $download->status,
                    'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
                    'retry_count' => $download->retry_count
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('下载重试失败', [
                'download_id' => $downloadId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '下载重试失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取下载统计
     */
    public function getDownloadStatistics(int $userId, array $params): array
    {
        try {
            $period = $params['period'] ?? 'month';
            $downloadType = $params['download_type'] ?? null;

            // 计算时间范围
            $dateRange = $this->getDateRange($period);
            
            $query = ResourceDownload::where('user_id', $userId)
                ->whereBetween('created_at', $dateRange);

            if ($downloadType) {
                $query->where('download_type', $downloadType);
            }

            $downloads = $query->get();

            // 基础统计
            $totalDownloads = $downloads->count();
            $successfulDownloads = $downloads->where('status', ResourceDownload::STATUS_COMPLETED)->count();
            $failedDownloads = $downloads->where('status', ResourceDownload::STATUS_FAILED)->count();
            $successRate = $totalDownloads > 0 ? round(($successfulDownloads / $totalDownloads) * 100, 2) : 0;

            // 大小统计
            $totalSize = $downloads->sum('file_size');
            $averageSize = $totalDownloads > 0 ? round($totalSize / $totalDownloads) : 0;

            // 类型分布
            $typeBreakdown = $downloads->groupBy('download_type')->map(function ($items, $type) use ($totalDownloads) {
                return [
                    'count' => $items->count(),
                    'percentage' => $totalDownloads > 0 ? round(($items->count() / $totalDownloads) * 100, 1) : 0
                ];
            });

            // 下载趋势
            $downloadTrends = $this->calculateDownloadTrends($downloads, $period);

            // 最常下载的类型
            $mostDownloadedType = $typeBreakdown->sortByDesc('count')->keys()->first();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'period' => $period,
                    'total_downloads' => $totalDownloads,
                    'successful_downloads' => $successfulDownloads,
                    'failed_downloads' => $failedDownloads,
                    'success_rate' => $successRate,
                    'total_size' => $this->formatFileSize($totalSize),
                    'average_size' => $this->formatFileSize($averageSize),
                    'most_downloaded_type' => $mostDownloadedType,
                    'download_trends' => $downloadTrends,
                    'type_breakdown' => $typeBreakdown
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取下载统计失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取下载统计失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 创建下载链接
     */
    public function createDownloadLink(int $userId, array $linkParams): array
    {
        try {
            DB::beginTransaction();

            // 验证目标资源权限
            $target = $this->getTargetResource($linkParams['target_type'], $linkParams['target_id'], $userId);
            if (!$target) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '目标资源不存在或无权限访问',
                    'data' => []
                ];
            }

            // 生成下载令牌
            $downloadToken = Str::random(64);
            $expiresAt = Carbon::now()->addSeconds($linkParams['expires_in']);

            // 创建下载记录
            $download = ResourceDownload::create([
                'user_id' => $userId,
                'download_type' => $linkParams['target_type'],
                'target_id' => $linkParams['target_id'],
                'target_name' => $linkParams['download_name'] ?? $target->getDownloadName(),
                'file_path' => $target->getFilePath(),
                'file_size' => $target->getFileSize(),
                'download_token' => $downloadToken,
                'status' => ResourceDownload::STATUS_READY,
                'expires_at' => $expiresAt,
                'user_agent' => request()->header('User-Agent'),
                'ip_address' => request()->ip(),
                'metadata' => [
                    'created_by' => 'download_management_service',
                    'target_type' => $linkParams['target_type']
                ]
            ]);

            // 缓存下载令牌信息
            Cache::put(
                'download_token:' . $downloadToken,
                [
                    'download_id' => $download->id,
                    'user_id' => $userId,
                    'target_type' => $linkParams['target_type'],
                    'target_id' => $linkParams['target_id']
                ],
                $expiresAt
            );

            DB::commit();

            Log::info('下载链接创建成功', [
                'download_id' => $download->id,
                'user_id' => $userId,
                'target_type' => $linkParams['target_type'],
                'target_id' => $linkParams['target_id']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '下载链接创建成功',
                'data' => [
                    'download_id' => $download->id,
                    'download_url' => route('api.downloads.secure', $downloadToken),
                    'target_type' => $linkParams['target_type'],
                    'target_id' => $linkParams['target_id'],
                    'file_name' => $download->target_name,
                    'file_size' => $this->formatFileSize($download->file_size),
                    'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
                    'created_at' => $download->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建下载链接失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '创建下载链接失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 安全下载
     */
    public function secureDownload(string $token)
    {
        try {
            // 从缓存获取令牌信息
            $tokenData = Cache::get('download_token:' . $token);
            if (!$tokenData) {
                return response()->json([
                    'code' => ApiCodeEnum::INVALID_TOKEN,
                    'message' => '下载令牌无效或已过期',
                    'data' => []
                ], 401);
            }

            // 获取下载记录
            $download = ResourceDownload::find($tokenData['download_id']);
            if (!$download) {
                return response()->json([
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '下载记录不存在',
                    'data' => []
                ], 404);
            }

            // 检查下载状态
            if ($download->status !== ResourceDownload::STATUS_READY) {
                return response()->json([
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '下载链接不可用',
                    'data' => []
                ], 400);
            }

            // 检查过期时间
            if ($download->expires_at && $download->expires_at->isPast()) {
                $download->update(['status' => ResourceDownload::STATUS_EXPIRED]);
                return response()->json([
                    'code' => ApiCodeEnum::EXPIRED,
                    'message' => '下载链接已过期',
                    'data' => []
                ], 410);
            }

            // 检查文件是否存在
            if (!$download->file_path || !Storage::exists($download->file_path)) {
                $download->update([
                    'status' => ResourceDownload::STATUS_FAILED,
                    'error_message' => '文件不存在'
                ]);
                return response()->json([
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '文件不存在',
                    'data' => []
                ], 404);
            }

            // 更新下载记录
            $download->update([
                'status' => ResourceDownload::STATUS_COMPLETED,
                'downloaded_at' => Carbon::now(),
                'download_count' => $download->download_count + 1
            ]);

            // 删除令牌缓存（一次性使用）
            Cache::forget('download_token:' . $token);

            // 记录下载日志
            Log::info('安全下载完成', [
                'download_id' => $download->id,
                'user_id' => $download->user_id,
                'file_path' => $download->file_path,
                'ip_address' => request()->ip()
            ]);

            // 返回文件下载响应
            $mimeType = Storage::mimeType($download->file_path);
            return Storage::download($download->file_path, $download->target_name, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'attachment; filename="' . $download->target_name . '"'
            ]);

        } catch (\Exception $e) {
            Log::error('安全下载失败', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '下载失败：' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * 创建批量下载
     */
    public function createBatchDownload(int $userId, array $batchParams): array
    {
        try {
            DB::beginTransaction();

            $batchId = 'batch_' . time() . '_' . $userId;
            $items = $batchParams['items'];
            $totalSize = 0;

            // 验证所有目标资源
            foreach ($items as $item) {
                $target = $this->getTargetResource($item['target_type'], $item['target_id'], $userId);
                if (!$target) {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => "资源 {$item['target_type']}:{$item['target_id']} 不存在或无权限访问",
                        'data' => []
                    ];
                }
                $totalSize += $target->getFileSize();
            }

            // 创建批量下载记录
            $batchDownload = ResourceDownload::create([
                'user_id' => $userId,
                'download_type' => 'batch',
                'target_id' => 0, // 批量下载没有单一目标ID
                'target_name' => $batchParams['archive_name'] . '.zip',
                'file_size' => $totalSize,
                'status' => ResourceDownload::STATUS_PREPARING,
                'batch_id' => $batchId,
                'expires_at' => Carbon::now()->addDays(7),
                'user_agent' => request()->header('User-Agent'),
                'ip_address' => request()->ip(),
                'metadata' => [
                    'items' => $items,
                    'compression_level' => $batchParams['compression_level'],
                    'item_count' => count($items)
                ]
            ]);

            DB::commit();

            // 异步创建批量下载文件
            $this->processBatchDownload($batchDownload);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量下载任务创建成功',
                'data' => [
                    'batch_id' => $batchId,
                    'download_url' => route('api.downloads.batch.status', $batchId),
                    'item_count' => count($items),
                    'estimated_size' => $this->formatFileSize($totalSize),
                    'status' => 'preparing',
                    'expires_at' => $batchDownload->expires_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建批量下载失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '创建批量下载失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 清理下载记录
     */
    public function cleanupDownloads(int $userId, array $cleanupParams): array
    {
        try {
            DB::beginTransaction();

            $daysOld = $cleanupParams['days_old'];
            $cleanupFiles = $cleanupParams['cleanup_files'];
            $cutoffDate = Carbon::now()->subDays($daysOld);

            // 获取需要清理的下载记录
            $downloads = ResourceDownload::where('user_id', $userId)
                ->where('created_at', '<', $cutoffDate)
                ->whereIn('status', [
                    ResourceDownload::STATUS_COMPLETED,
                    ResourceDownload::STATUS_FAILED,
                    ResourceDownload::STATUS_EXPIRED
                ])
                ->get();

            $cleanedRecords = 0;
            $deletedFiles = 0;
            $freedSpace = 0;

            foreach ($downloads as $download) {
                // 删除文件
                if ($cleanupFiles && $download->file_path && Storage::exists($download->file_path)) {
                    $fileSize = Storage::size($download->file_path);
                    Storage::delete($download->file_path);
                    $deletedFiles++;
                    $freedSpace += $fileSize;
                }

                // 删除记录
                $download->delete();
                $cleanedRecords++;
            }

            DB::commit();

            Log::info('下载记录清理完成', [
                'user_id' => $userId,
                'cleaned_records' => $cleanedRecords,
                'deleted_files' => $deletedFiles,
                'freed_space' => $freedSpace
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '下载记录清理完成',
                'data' => [
                    'cleaned_records' => $cleanedRecords,
                    'deleted_files' => $deletedFiles,
                    'freed_space' => $this->formatFileSize($freedSpace),
                    'cleanup_date' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('清理下载记录失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '清理下载记录失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function calculateDownloadStatistics(int $userId, array $filters): array
    {
        $query = ResourceDownload::where('user_id', $userId);
        
        // 应用相同的过滤条件
        if (!empty($filters['download_type'])) {
            $query->where('download_type', $filters['download_type']);
        }

        $allDownloads = $query->get();
        $thisMonth = $query->whereMonth('created_at', Carbon::now()->month)->count();
        $totalSize = $allDownloads->sum('file_size');
        $successCount = $allDownloads->where('status', ResourceDownload::STATUS_COMPLETED)->count();
        $successRate = $allDownloads->count() > 0 ? round(($successCount / $allDownloads->count()) * 100, 1) : 0;

        return [
            'total_downloads' => $allDownloads->count(),
            'total_size' => $this->formatFileSize($totalSize),
            'this_month' => $thisMonth,
            'success_rate' => $successRate
        ];
    }

    private function getDateRange(string $period): array
    {
        switch ($period) {
            case 'day':
                return [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()];
            case 'week':
                return [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
            case 'month':
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
            case 'year':
                return [Carbon::now()->startOfYear(), Carbon::now()->endOfYear()];
            default:
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
        }
    }

    private function calculateDownloadTrends($downloads, string $period): array
    {
        // 简化的趋势计算
        $trends = [];
        $groupBy = $period === 'day' ? 'H' : 'd';
        
        $grouped = $downloads->groupBy(function ($download) use ($groupBy) {
            return $download->created_at->format($groupBy === 'H' ? 'Y-m-d H' : 'Y-m-d');
        });

        foreach ($grouped as $date => $items) {
            $trends[] = [
                'date' => $date,
                'count' => $items->count(),
                'size' => $this->formatFileSize($items->sum('file_size'))
            ];
        }

        return array_slice($trends, -10); // 返回最近10个数据点
    }

    private function checkTargetAvailability(string $targetType, int $targetId): bool
    {
        switch ($targetType) {
            case 'resource':
                return Resource::where('id', $targetId)
                    ->where('status', Resource::STATUS_READY)
                    ->exists();
            case 'export':
                return ResourceExport::where('id', $targetId)
                    ->where('status', ResourceExport::STATUS_COMPLETED)
                    ->exists();
            case 'version':
                return ResourceVersion::where('id', $targetId)
                    ->where('status', ResourceVersion::STATUS_COMPLETED)
                    ->exists();
            default:
                return false;
        }
    }

    private function getTargetResource(string $targetType, int $targetId, int $userId)
    {
        switch ($targetType) {
            case 'resource':
                return Resource::where('id', $targetId)
                    ->where('user_id', $userId)
                    ->where('status', Resource::STATUS_READY)
                    ->first();
            case 'export':
                return ResourceExport::where('id', $targetId)
                    ->where('user_id', $userId)
                    ->where('status', ResourceExport::STATUS_COMPLETED)
                    ->first();
            case 'version':
                return ResourceVersion::where('id', $targetId)
                    ->where('user_id', $userId)
                    ->where('status', ResourceVersion::STATUS_COMPLETED)
                    ->first();
            default:
                return null;
        }
    }

    private function processBatchDownload(ResourceDownload $batchDownload): void
    {
        // 这里应该异步处理批量下载文件的创建
        // 简化实现，实际项目中可能需要队列处理
        try {
            $batchDownload->update(['status' => ResourceDownload::STATUS_READY]);
        } catch (\Exception $e) {
            $batchDownload->update([
                'status' => ResourceDownload::STATUS_FAILED,
                'error_message' => $e->getMessage()
            ]);
        }
    }

    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . $units[$pow];
    }
}
