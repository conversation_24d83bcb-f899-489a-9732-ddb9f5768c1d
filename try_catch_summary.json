{"detection_time": "2025-08-01 22:18:33", "total_controllers": 41, "total_methods": 253, "controllers_without_log_import": 0, "methods_without_try_catch": 27, "methods_with_invalid_catch": 226, "controllers_without_log_import_list": [], "methods_without_try_catch_list": [{"controller": "AdController.php", "method": "ad_store", "line": 38}, {"controller": "AdController.php", "method": "ad_update", "line": 99}, {"controller": "AdController.php", "method": "store", "line": 150}, {"controller": "AdController.php", "method": "update", "line": 196}, {"controller": "AnalyticsController.php", "method": "getAiPerformance", "line": 282}, {"controller": "AnalyticsController.php", "method": "getUserRetention", "line": 368}, {"controller": "AnalyticsController.php", "method": "getRevenue", "line": 456}, {"controller": "AnalyticsController.php", "method": "generateCustomReport", "line": 517}, {"controller": "CacheController.php", "method": "warmupCache", "line": 205}, {"controller": "CacheController.php", "method": "get<PERSON><PERSON><PERSON>", "line": 287}, {"controller": "CacheController.php", "method": "getValue", "line": 355}, {"controller": "CacheController.php", "method": "setValue", "line": 417}, {"controller": "CacheController.php", "method": "deleteKeys", "line": 489}, {"controller": "CacheController.php", "method": "getConfig", "line": 578}, {"controller": "DownloadController.php", "method": "retry", "line": 153}, {"controller": "DownloadController.php", "method": "statistics", "line": 206}, {"controller": "DownloadController.php", "method": "createLink", "line": 265}, {"controller": "DownloadController.php", "method": "secureDownload", "line": 321}, {"controller": "DownloadController.php", "method": "batchDownload", "line": 357}, {"controller": "DownloadController.php", "method": "cleanup", "line": 425}, {"controller": "LogController.php", "method": "resolveError", "line": 486}, {"controller": "LogController.php", "method": "exportLogs", "line": 538}, {"controller": "WorkPublishController.php", "method": "publishWork", "line": 42}, {"controller": "WorkPublishController.php", "method": "update", "line": 144}, {"controller": "WorkPublishController.php", "method": "delete", "line": 231}, {"controller": "WorkPublishController.php", "method": "getShareLink", "line": 363}, {"controller": "WorkPublishController.php", "method": "like", "line": 429}], "methods_with_invalid_catch_count_by_controller": {"AiGenerationController.php": 4, "AiModelController.php": 14, "AiTaskController.php": 8, "AnalyticsController.php": 2, "AssetController.php": 4, "AudioController.php": 4, "AuthController.php": 6, "BatchController.php": 7, "CacheController.php": 2, "CharacterController.php": 9, "ConfigController.php": 7, "CreditsController.php": 3, "DownloadController.php": 1, "FileController.php": 5, "ImageController.php": 4, "LogController.php": 4, "MusicController.php": 4, "NotificationController.php": 6, "PermissionController.php": 7, "PointsController.php": 3, "ProjectController.php": 9, "ProjectManagementController.php": 6, "PublicationController.php": 8, "RecommendationController.php": 8, "ResourceController.php": 8, "ReviewController.php": 7, "SocialController.php": 9, "SoundController.php": 4, "StoryController.php": 2, "StyleController.php": 4, "TaskManagementController.php": 5, "TemplateController.php": 7, "UserController.php": 4, "UserGrowthController.php": 10, "VersionController.php": 6, "VideoController.php": 3, "VoiceController.php": 7, "WebSocketController.php": 4, "WorkPublishController.php": 3, "WorkflowController.php": 8}}