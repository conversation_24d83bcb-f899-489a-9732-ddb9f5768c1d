<?php

namespace App\Http\Controllers;

use App\Enums\ApiCodeEnum;
use App\Exceptions\ApiException;
use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;
use Illuminate\Contracts\Validation\Factory as ValidationFactory;

class Controller extends BaseController
{
    /**
     * @param array $data
     * @param array $rules
     * @param array $messages
     * @param array $attributes
     */
    public function validateData(array $data, array $rules, array $messages = [], array $attributes = [])
    {
        $validator = $this->getValidationFactory()->make($data, $rules, $messages, $attributes);
        if ($validator->fails())
        {
            throw new ApiException($validator->errors()->first(),ApiCodeEnum::VALIDATION_ERROR);
        }
    }

    /**
     * Get the validation factory instance.
     *
     * @return \Illuminate\Contracts\Validation\Factory
     */
    protected function getValidationFactory()
    {
        return app(ValidationFactory::class);
    }

    /**
     * 统一成功响应格式
     * 
     * @param mixed $data 响应数据
     * @param string|null $message 响应消息
     * @param int|null $code 业务状态码
     * @return \Illuminate\Http\JsonResponse
     */
    protected function successResponse($data = null, string $message = '', int $code = null)
    {
        $result = [
            'code' => $code ?? ApiCodeEnum::SUCCESS,
            'message' => $message ?? ApiCodeEnum::getDescription($code ?? ApiCodeEnum::SUCCESS) ?? '操作成功',
            'data' => $data,
            'timestamp' => time(),
            'request_id' => $this->generateRequestId()
        ];

        return $this->formatResponse($result);
    }

    /**
     * 统一错误响应格式
     * 
     * @param int $code 业务错误码
     * @param string|null $message 错误消息
     * @param mixed $data 错误数据
     * @return \Illuminate\Http\JsonResponse
     */
    protected function errorResponse(int $code, string $message = '', $data = null)
    {
        $result = [
            'code' => $code,
            'message' => $message ?? ApiCodeEnum::getDescription($code) ?? '操作失败',
            'data' => $data,
            'timestamp' => time(),
            'request_id' => $this->generateRequestId()
        ];

        return $this->formatResponse($result);
    }

    /**
     * 格式化响应，根据业务错误码返回正确的HTTP状态码
     * 
     * @param array $result 响应结果数组
     * @return \Illuminate\Http\JsonResponse
     */
    protected function formatResponse(array $result)
    {
        $httpCode = $this->getHttpStatusCode($result['code']);
        return response()->json($result, $httpCode);
    }

    /**
     * 生成请求ID
     * 
     * @return string
     */
    private function generateRequestId(): string
    {
        return 'req_' . uniqid() . '_' . substr(md5(microtime()), 0, 8);
    }

    /**
     * 根据业务状态码获取HTTP状态码 (重构优化版)
     *
     * @param int $businessCode 业务状态码
     * @return int HTTP状态码
     */
    private function getHttpStatusCode(int $businessCode): int
    {
        switch ($businessCode) {
            // --- 2xx 成功 ---
            case ApiCodeEnum::SUCCESS:
                return 200;

            // --- 400 Bad Request (通用客户端错误) ---
            // 请求本身有语法问题或无法被服务器理解
            case ApiCodeEnum::FAIL:
            case ApiCodeEnum::BAD_REQUEST:
            case ApiCodeEnum::INVALID_OPERATION: // 操作无效，通常是客户端发起了不合逻辑的请求
                return 400;

            // --- 401 Unauthorized (认证失败) ---
            // 缺少、无效或过期的身份凭证
            case ApiCodeEnum::UNAUTHORIZED:
            case ApiCodeEnum::INVALID_TOKEN:
            case ApiCodeEnum::EXPIRED: // Token 过期本质上是认证失败
                return 401;

            // --- 403 Forbidden (授权失败) ---
            // 服务器理解请求，但拒绝执行。认证成功，但无权限。
            case ApiCodeEnum::FORBIDDEN:
            case ApiCodeEnum::PERMISSION_DENIED:
            case ApiCodeEnum::PROJECT_ACCESS_DENIED:
                return 403;

            // --- 404 Not Found (资源未找到) ---
            case ApiCodeEnum::NOT_FOUND:
            case ApiCodeEnum::PROJECT_NOT_FOUND:
            case ApiCodeEnum::CHARACTER_NOT_FOUND:
            case ApiCodeEnum::FILE_NOT_FOUND:
            case ApiCodeEnum::MODEL_NOT_FOUND:
            case ApiCodeEnum::CLINE_VOICE_NOT_FOUND:
            case ApiCodeEnum::USER_NOT_REGISTERED: // 尝试操作一个不存在的用户
                return 404;

            // --- 405 Method Not Allowed ---
            case ApiCodeEnum::METHOD_NOT_ALLOWED:
                return 405;

            // --- 409 Conflict (状态冲突) ---
            // 请求与服务器当前状态冲突，常用于创建已存在的资源
            case ApiCodeEnum::CONFLICT:
            case ApiCodeEnum::USER_ALREADY_EXISTS:
            case ApiCodeEnum::EMAIL_ALREADY_EXISTS:
            case ApiCodeEnum::DUPLICATE_OPERATION:
                return 409;

            // --- 413 Payload Too Large (请求体过大) ---
            case ApiCodeEnum::FILE_TOO_LARGE:
            case ApiCodeEnum::INPUT_TOO_LONG:
                return 413;

            // --- 415 Unsupported Media Type (不支持的媒体类型) ---
            case ApiCodeEnum::FILE_TYPE_NOT_SUPPORTED:
                return 415;

            // --- 422 Unprocessable Entity (语义错误/验证失败) ---
            // 请求格式正确，但内容包含语义错误，服务器无法处理
            case ApiCodeEnum::VALIDATION_ERROR:
            case ApiCodeEnum::INVALID_PARAMS:
            case ApiCodeEnum::INVALID_PARAMETER:
            case ApiCodeEnum::CONTENT_FILTERED: // 内容因敏感词等原因被拒，是一种业务验证失败
                return 422;

            // --- 429 Too Many Requests (请求过于频繁) ---
            // 用户在给定时间内发送了太多请求（速率限制或配额）
            case ApiCodeEnum::CONNECTION_LIMIT:
            case ApiCodeEnum::LIMIT_EXCEEDED:
            case ApiCodeEnum::INSUFFICIENT_POINTS: // 可视为一种配额耗尽
            case ApiCodeEnum::QUOTA_EXCEEDED:
            case ApiCodeEnum::PROJECT_LIMIT_EXCEEDED:
            case ApiCodeEnum::CHARACTER_LIMIT_EXCEEDED:
            case ApiCodeEnum::EVENT_COUNT_LIMIT:
            case ApiCodeEnum::RATE_LIMIT_EXCEEDED:
                return 429;

            // --- 500 Internal Server Error (服务器内部错误) ---
            // 服务器遇到了不知道如何处理的意外情况
            case ApiCodeEnum::ERROR:
            case ApiCodeEnum::SYSTEM_ERROR:
            case ApiCodeEnum::INTERNAL_ERROR:
            case ApiCodeEnum::AI_SERVICE_ERROR:     // 我方服务调用AI服务失败，是'我方'的内部错误
            case ApiCodeEnum::GENERATION_FAILED:    // 未预料到的生成失败
            case ApiCodeEnum::UPLOAD_FAILED:         // 未预料到的上传失败
            case ApiCodeEnum::CHARACTER_BINDING_FAILED:
            case ApiCodeEnum::TEMPLATE_ADD_FAILED:
            case ApiCodeEnum::CONTROLLER_ERROR:     // 在catch块中捕获
            case ApiCodeEnum::MY_SERVICE_ERROR:       // 在catch块中捕获
                return 500;
            
            // --- 503 Service Unavailable (服务不可用) ---
            // 服务器暂时无法处理请求，通常是由于维护或过载
            case ApiCodeEnum::SERVICE_UNAVAILABLE:
            case ApiCodeEnum::MAINTENANCE_MODE:
            case ApiCodeEnum::MODEL_NOT_AVAILABLE: // 依赖的核心模型暂时不可用
                return 503;

            // --- 504 Gateway Timeout (网关超时) ---
            case ApiCodeEnum::PROCESSING_TIMEOUT: // 我方作为网关调用AI服务时超时
                return 504;

            // --- 安全的默认值 ---
            // 对于任何未明确映射的业务码，都应视为未知的服务器内部错误。
            // 这可以防止将新的错误码意外地作为200 OK返回。
            default:
                return 500;
        }
    }

    /**
     * 清理和脱敏请求数据以用于日志记录
     * @param array $data
     * @return array
     */
    protected function sanitize_request_for_log(array $data): array
    {
        $sensitiveKeys = [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'credit_card_number',
            'cvv'
        ];

        foreach ($sensitiveKeys as $key) {
            if (array_key_exists($key, $data)) {
                // 将敏感字段的值替换为掩码
                $data[$key] = '********';
            }
        }

        // 处理可能的超大字段或文件上传
        foreach ($data as $key => &$value) {
            if (is_string($value) && mb_strlen($value) > 1024) {
                // 截断过长的字符串
                $value = mb_substr($value, 0, 1024) . '... [TRUNCATED]';
            }
        }

        return $data;
    }
}
