[2025-08-02 08:54:22] production.ERROR: include(D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): <PERSON><PERSON>\\Lumen\\Application->handleError(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\longtool\\\\php...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(250): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\A...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\bootstrap\\app.php(100): Laravel\\Lumen\\Application->register('App\\\\Providers\\\\A...')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(14): require('D:\\\\longtool\\\\php...')
#7 {main}
"} 
[2025-08-02 08:54:25] production.ERROR: include(D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\longtool\\\\php...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(250): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\A...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\bootstrap\\app.php(100): Laravel\\Lumen\\Application->register('App\\\\Providers\\\\A...')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(14): require('D:\\\\longtool\\\\php...')
#7 {main}
"} 
[2025-08-02 08:54:30] production.ERROR: include(D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\longtool\\\\php...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(250): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\A...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\bootstrap\\app.php(100): Laravel\\Lumen\\Application->register('App\\\\Providers\\\\A...')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(14): require('D:\\\\longtool\\\\php...')
#7 {main}
"} 
[2025-08-02 08:54:30] production.ERROR: include(D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\longtool\\\\php...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(250): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\A...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\bootstrap\\app.php(100): Laravel\\Lumen\\Application->register('App\\\\Providers\\\\A...')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(14): require('D:\\\\longtool\\\\php...')
#7 {main}
"} 
[2025-08-02 08:55:41] production.ERROR: include(D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\longtool\\\\php...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(250): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\A...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\bootstrap\\app.php(100): Laravel\\Lumen\\Application->register('App\\\\Providers\\\\A...')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(14): require('D:\\\\longtool\\\\php...')
#7 {main}
"} 
[2025-08-02 08:55:41] production.ERROR: include(D:\longtool\phpStudy_64\WWW\tool_api\php\api\vendor\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer/../../app/Providers/AppServiceProvider.php): Failed to open stream: No such file or directory at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Concerns\\RegistersExceptionHandlers.php(47): Laravel\\Lumen\\Application->handleError(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): Laravel\\Lumen\\Application->Laravel\\Lumen\\Concerns\\{closure}(2, 'include(D:\\\\long...', 'D:\\\\longtool\\\\php...', 576)
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\longtool\\\\php...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(250): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\A...')
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\bootstrap\\app.php(100): Laravel\\Lumen\\Application->register('App\\\\Providers\\\\A...')
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\public\\index.php(14): require('D:\\\\longtool\\\\php...')
#7 {main}
"} 
