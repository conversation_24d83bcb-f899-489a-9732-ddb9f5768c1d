<?php

/**
 * 全面检测API控制器的try-catch架构
 * 检测要求：
 * 1. 方法名对下第一行必须包含"try {"
 * 2. catch块中必须存在"return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR"
 * 3. 控制器中必须导入"Log"
 */

class ControllerTryCatchChecker
{
    private $controllerPath = 'D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api';
    private $results = [];
    private $totalControllers = 0;
    private $totalMethods = 0;
    private $issuesFound = [];

    public function checkAllControllers()
    {
        echo "开始检测API控制器的try-catch架构...\n";
        echo "控制器路径: {$this->controllerPath}\n";
        echo str_repeat('=', 80) . "\n";

        $files = glob($this->controllerPath . '/*.php');
        $this->totalControllers = count($files);
        
        echo "发现 {$this->totalControllers} 个控制器文件\n\n";

        foreach ($files as $file) {
            $this->checkController($file);
        }

        $this->generateReport();
    }

    private function checkController($filePath)
    {
        $fileName = basename($filePath);
        echo "检测控制器: {$fileName}\n";
        
        $content = file_get_contents($filePath);
        if (!$content) {
            echo "  错误: 无法读取文件\n";
            return;
        }

        // 检查是否导入Log
        $hasLogImport = $this->checkLogImport($content, $fileName);
        
        // 获取所有方法
        $methods = $this->extractMethods($content);
        $this->totalMethods += count($methods);
        
        echo "  发现 " . count($methods) . " 个方法\n";
        
        foreach ($methods as $method) {
            $this->checkMethodTryCatch($content, $method, $fileName);
        }
        
        $this->results[$fileName] = [
            'hasLogImport' => $hasLogImport,
            'methods' => $methods,
            'methodCount' => count($methods)
        ];
        
        echo "\n";
    }

    private function checkLogImport($content, $fileName)
    {
        // 检查是否有Log导入
        $logImportPatterns = [
            '/use\s+Illuminate\\Support\\Facades\\Log;/',
            '/use\s+Log;/',
            '/Illuminate\\Support\\Facades\\Log::/',
            '/Log::/',
        ];
        
        $hasLogImport = false;
        foreach ($logImportPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $hasLogImport = true;
                break;
            }
        }
        
        if (!$hasLogImport) {
            $this->issuesFound[] = "控制器 {$fileName}: 未导入Log";
            echo "  ❌ 未导入Log\n";
        } else {
            echo "  ✅ 已导入Log\n";
        }
        
        return $hasLogImport;
    }

    private function extractMethods($content)
    {
        // 匹配所有public方法，排除构造函数
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)/', $content, $matches);
        
        $methods = [];
        foreach ($matches[1] as $methodName) {
            // 排除构造函数
            if ($methodName !== '__construct') {
                $methods[] = $methodName;
            }
        }
        
        return $methods;
    }

    private function checkMethodTryCatch($content, $methodName, $fileName)
    {
        // 获取方法的完整内容
        $pattern = '/public\s+function\s+' . preg_quote($methodName) . '\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/s';
        
        if (preg_match($pattern, $content, $matches)) {
            $methodContent = $matches[1];
            
            // 检查方法第一行是否包含 "try {"
            $lines = explode("\n", trim($methodContent));
            $firstNonEmptyLine = '';
            foreach ($lines as $line) {
                $trimmedLine = trim($line);
                if (!empty($trimmedLine) && !preg_match('/^\s*\/\*/', $trimmedLine) && !preg_match('/^\s*\/\//', $trimmedLine) && !preg_match('/^\s*\*/', $trimmedLine)) {
                    $firstNonEmptyLine = $trimmedLine;
                    break;
                }
            }
            
            $hasTryStart = strpos($firstNonEmptyLine, 'try {') !== false;
            
            // 检查是否有正确的catch块
            $hasCorrectCatch = strpos($methodContent, 'return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR') !== false;
            
            if (!$hasTryStart) {
                $this->issuesFound[] = "控制器 {$fileName}, 方法 {$methodName}: 第一行未包含 'try {'";
                echo "    ❌ {$methodName}: 第一行未包含 'try {'\n";
            } else {
                echo "    ✅ {$methodName}: 第一行包含 'try {'\n";
            }
            
            if (!$hasCorrectCatch) {
                $this->issuesFound[] = "控制器 {$fileName}, 方法 {$methodName}: catch块中未包含正确的错误响应";
                echo "    ❌ {$methodName}: catch块中未包含 'return \$this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR'\n";
            } else {
                echo "    ✅ {$methodName}: catch块包含正确的错误响应\n";
            }
        } else {
            echo "    ❌ {$methodName}: 无法解析方法内容\n";
            $this->issuesFound[] = "控制器 {$fileName}, 方法 {$methodName}: 无法解析方法内容";
        }
    }

    private function generateReport()
    {
        echo str_repeat('=', 80) . "\n";
        echo "检测完成报告\n";
        echo str_repeat('=', 80) . "\n";
        echo "总控制器数量: {$this->totalControllers}\n";
        echo "总方法数量: {$this->totalMethods}\n";
        echo "发现问题数量: " . count($this->issuesFound) . "\n\n";
        
        if (empty($this->issuesFound)) {
            echo "🎉 所有控制器都符合try-catch架构要求！\n";
        } else {
            echo "❌ 发现以下问题:\n";
            echo str_repeat('-', 80) . "\n";
            foreach ($this->issuesFound as $index => $issue) {
                echo ($index + 1) . ". {$issue}\n";
            }
        }
        
        echo "\n" . str_repeat('=', 80) . "\n";
        echo "详细统计:\n";
        foreach ($this->results as $fileName => $result) {
            echo "- {$fileName}: {$result['methodCount']} 个方法\n";
        }
    }
}

// 执行检测
$checker = new ControllerTryCatchChecker();
$checker->checkAllControllers();

echo "\n检测完成！\n";
?>