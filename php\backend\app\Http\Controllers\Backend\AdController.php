<?php

namespace App\Http\Controllers\Backend;

use App\Models\Ad;
use Illuminate\Http\Request;
use App\Actions\Backend\Jump;
use App\Actions\Backend\Search;
use App\Http\Controllers\Controller;
use App\Http\Requests\Backend\Ad\StoreRequest;
use App\Http\Requests\Backend\Ad\UpdateRequest;
use Illuminate\Support\Facades\Log;
use App\Enums\ApiCodeEnum;

class AdController extends Controller
{
    use Search,Jump;

    /**
     * 是否是关联查询
     */
    protected $relationSearch = false;

    /**
     * 快速搜索字段
     * @var string[]
     */
    protected $searchFields = ['id'];

    /**
     * @param Request $request
     * @return mixed
     */
    public function index(Request $request){
        try {
            if($request->ajax()){
                list($where, $sortName, $sortOrder, $pageSize) = $this->buildparams($request);
                $results = Ad::query()
                    ->where($where)
                    ->orderBy($sortName,$sortOrder)
                    ->paginate($pageSize,['*'],'pageNumber');
                $total = $results->total();
                $rows = $results->items();
                return compact('total','rows');
            }
            return view('backend/ad/index');
        } catch (\Exception $e) {
            Log::error('广告列表获取失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告列表获取失败');
        }
    }

    /**
     * @return mixed
     */
    public function create(){
        try {
            return view('backend/ad/create');
        } catch (\Exception $e) {
            Log::error('广告创建页面加载失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => [],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告创建页面加载失败');
        }
    }

    /**
     * @param StoreRequest $request
     * @return mixed
     */
    public function store(StoreRequest $request){
        try {
            $ad = new Ad();
            $params = $request->only($ad->getFillable());
            if(empty($params['id'])){
                unset($params['id']);
            }
            $ad->fill($params);
            if($ad->save()){
                return $this->success();
            }
            return $this->error();
        } catch (\Exception $e) {
            Log::error('广告创建失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告创建失败');
        }
    }

    /**
     * @param Ad $ad
     * @return mixed
     */
    public function edit (Ad $ad){
        try {
            return view('backend/ad/edit',compact('ad'));
        } catch (\Exception $e) {
            Log::error('广告编辑页面加载失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => ['ad_id' => $ad->id ?? null],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告编辑页面加载失败');
        }
    }

    /**
     * @param Ad $ad
     * @param UpdateRequest $request
     * @return mixed
     */
    public function update (Ad $ad,UpdateRequest $request){
        try {
            $params = $request->only($ad->getFillable());
            if(empty($params['id'])){
                unset($params['id']);
            }
            $ad->fill($params);
            if($ad->save()){
                return $this->success();
            }
            return $this->error();
        } catch (\Exception $e) {
            Log::error('广告更新失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告更新失败');
        }
    }

    /**
     * @param Ad $ad
     * @return mixed
     */
    public function destroy (Ad $ad){
        try {
            if($ad->delete()){
                return $this->success();
            }
            return $this->error();
        } catch (\Exception $e) {
            Log::error('广告删除失败', [
                'method' => __METHOD__,
                'user_id' => null,
                'request_data' => ['ad_id' => $ad->id ?? null],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '广告删除失败');
        }
    }
}
