<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\SoundService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * AI音效生成与声音处理
 */
class SoundController extends Controller
{
    protected $soundService;

    public function __construct(SoundService $soundService)
    {
        $this->soundService = $soundService;
    }

    /**
     * @ApiTitle(音效生成)
     * @ApiSummary(使用AI生成音效)
     * @ApiMethod(POST)
     * @ApiRoute(/api/sounds/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="音效生成提示词")
     * @ApiParams(name="category", type="string", required=false, description="音效分类：environment/action/ui/ambient/nature")
     * @ApiParams(name="duration", type="int", required=false, description="音效时长（秒）")
     * @ApiParams(name="volume", type="float", required=false, description="音量：0.1-1.0")
     * @ApiParams(name="loop", type="boolean", required=false, description="是否循环")
     * @ApiParams(name="fade_in", type="float", required=false, description="淡入时间（秒）")
     * @ApiParams(name="fade_out", type="float", required=false, description="淡出时间（秒）")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="platform", type="string", required=false, description="指定AI平台：volcengine/minimax")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "音效生成任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "estimated_cost": "0.0500",
     *     "platform": "volcengine"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        try {
            $rules = [
                'prompt' => 'required|string|min:3|max:500',
                'category' => 'sometimes|string|in:environment,action,ui,ambient,nature,mechanical,human,animal',
                'duration' => 'sometimes|integer|min:1|max:60',
                'volume' => 'sometimes|numeric|min:0.1|max:1.0',
                'loop' => 'sometimes|boolean',
                'fade_in' => 'sometimes|numeric|min:0|max:10',
                'fade_out' => 'sometimes|numeric|min:0|max:10',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'platform' => 'sometimes|string|in:volcengine,minimax'
            ];

            $messages = [
                'prompt.required' => '音效生成提示词不能为空',
                'prompt.min' => '音效生成提示词至少3个字符',
                'prompt.max' => '音效生成提示词不能超过500个字符',
                'category.in' => '音效分类必须是：environment、action、ui、ambient、nature、mechanical、human、animal之一',
                'duration.min' => '音效时长至少1秒',
                'duration.max' => '音效时长不能超过60秒',
                'volume.min' => '音量不能小于0.1',
                'volume.max' => '音量不能大于1.0',
                'fade_in.min' => '淡入时间不能小于0',
                'fade_in.max' => '淡入时间不能超过10秒',
                'fade_out.min' => '淡出时间不能小于0',
                'fade_out.max' => '淡出时间不能超过10秒',
                'project_id.exists' => '项目不存在',
                'platform.in' => 'AI平台必须是：volcengine、minimax之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $generationParams = [
                'category' => $request->get('category', 'environment'),
                'duration' => $request->get('duration', 5),
                'volume' => $request->get('volume', 0.8),
                'loop' => $request->get('loop', false),
                'fade_in' => $request->get('fade_in', 0),
                'fade_out' => $request->get('fade_out', 0),
                'platform' => $request->get('platform', 'volcengine')
            ];

            $result = $this->soundService->generateSound(
                $user->id,
                $request->prompt,
                $request->project_id,
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('音效生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '音效生成失败');
        }
    }

    /**
     * @ApiTitle(音效生成状态查询)
     * @ApiSummary(查询音效生成任务的状态和结果)
     * @ApiMethod(GET)
     * @ApiRoute(/api/sounds/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "sound_generation",
     *     "status": "completed",
     *     "platform": "volcengine",
     *     "audio_url": "https://aiapi.tiptop.cn/sounds/generated/123.wav",
     *     "duration": 5,
     *     "file_size": "0.8MB",
     *     "cost": "0.0500",
     *     "processing_time_ms": 15000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:15"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->soundService->getSoundStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取音效生成状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取音效生成状态失败');
        }
    }

    /**
     * @ApiTitle(音效生成结果获取)
     * @ApiSummary(获取音效生成的详细结果和下载信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/sounds/{id}/result)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "task_id": 123,
     *     "audio_url": "https://aiapi.tiptop.cn/sounds/generated/123.wav",
     *     "waveform_url": "https://aiapi.tiptop.cn/sounds/waveforms/123.png",
     *     "metadata": {
     *       "duration": 5,
     *       "format": "wav",
     *       "sample_rate": "44100Hz",
     *       "file_size": "0.8MB",
     *       "category": "environment",
     *       "volume": 0.8,
     *       "loop": false
     *     },
     *     "download_info": {
     *       "direct_url": "https://aiapi.tiptop.cn/sounds/generated/123.wav",
     *       "expires_at": "2024-01-08 12:00:00"
     *     }
     *   }
     * })
     */
    public function getResult(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->soundService->getSoundResult($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取音效生成结果失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取音效生成结果失败');
        }
    }

    /**
     * @ApiTitle(批量音效生成)
     * @ApiSummary(批量生成多个音效)
     * @ApiMethod(POST)
     * @ApiRoute(/api/batch/sounds/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompts", type="array", required=true, description="音效提示词数组")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="common_params", type="object", required=false, description="通用参数")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量音效生成任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123",
     *     "task_ids": [123, 124, 125],
     *     "total_count": 3,
     *     "estimated_cost": "0.1500"
     *   }
     * })
     */
    public function batchGenerate(Request $request)
    {
        try {
            $rules = [
                'prompts' => 'required|array|min:1|max:20',
                'prompts.*' => 'required|string|min:3|max:500',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'common_params' => 'sometimes|array'
            ];

            $messages = [
                'prompts.required' => '提示词数组不能为空',
                'prompts.min' => '至少需要1个提示词',
                'prompts.max' => '最多支持20个提示词',
                'prompts.*.required' => '提示词不能为空',
                'prompts.*.min' => '提示词至少3个字符',
                'prompts.*.max' => '提示词不能超过500个字符',
                'project_id.exists' => '项目不存在'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->soundService->batchGenerateSounds(
                $user->id,
                $request->prompts,
                $request->project_id,
                $request->get('common_params', [])
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量音效生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量音效生成失败');
        }
    }
}
