<?php

/**
 * 详细的try-catch架构检测报告生成器
 */

class DetailedTryCatchReporter
{
    private $controllerPath = 'D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\Api';
    private $results = [];
    private $totalControllers = 0;
    private $totalMethods = 0;
    private $issuesFound = [];
    private $reportFile = 'try_catch_detailed_report.md';

    public function generateDetailedReport()
    {
        $this->checkAllControllers();
        $this->writeReportToFile();
        echo "详细报告已生成: {$this->reportFile}\n";
    }

    private function checkAllControllers()
    {
        $files = glob($this->controllerPath . '/*.php');
        $this->totalControllers = count($files);
        
        foreach ($files as $file) {
            $this->checkController($file);
        }
    }

    private function checkController($filePath)
    {
        $fileName = basename($filePath);
        $content = file_get_contents($filePath);
        
        if (!$content) {
            return;
        }

        // 检查Log导入
        $hasLogImport = $this->checkLogImport($content);
        
        // 获取所有方法
        $methods = $this->extractMethods($content);
        $this->totalMethods += count($methods);
        
        $methodResults = [];
        foreach ($methods as $method) {
            $methodResults[$method] = $this->checkMethodTryCatch($content, $method, $fileName);
        }
        
        $this->results[$fileName] = [
            'hasLogImport' => $hasLogImport,
            'methods' => $methodResults,
            'methodCount' => count($methods)
        ];
    }

    private function checkLogImport($content)
    {
        $logImportPatterns = [
            '/use\s+Illuminate\\Support\\Facades\\Log;/',
            '/use\s+Log;/',
        ];
        
        foreach ($logImportPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }

    private function extractMethods($content)
    {
        preg_match_all('/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)/', $content, $matches);
        
        $methods = [];
        foreach ($matches[1] as $methodName) {
            if ($methodName !== '__construct') {
                $methods[] = $methodName;
            }
        }
        
        return $methods;
    }

    private function checkMethodTryCatch($content, $methodName, $fileName)
    {
        // 更精确的方法匹配
        $pattern = '/public\s+function\s+' . preg_quote($methodName) . '\s*\([^)]*\)\s*\{/s';
        
        if (preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
            $startPos = $matches[0][1] + strlen($matches[0][0]);
            
            // 找到方法的结束位置
            $braceCount = 1;
            $pos = $startPos;
            $methodContent = '';
            
            while ($pos < strlen($content) && $braceCount > 0) {
                $char = $content[$pos];
                $methodContent .= $char;
                
                if ($char === '{') {
                    $braceCount++;
                } elseif ($char === '}') {
                    $braceCount--;
                }
                $pos++;
            }
            
            // 移除最后的}
            $methodContent = rtrim($methodContent, '}');
            
            // 检查第一行是否包含try
            $lines = explode("\n", trim($methodContent));
            $firstNonEmptyLine = '';
            foreach ($lines as $line) {
                $trimmedLine = trim($line);
                if (!empty($trimmedLine) && 
                    !preg_match('/^\s*\/\*/', $trimmedLine) && 
                    !preg_match('/^\s*\/\//', $trimmedLine) && 
                    !preg_match('/^\s*\*/', $trimmedLine)) {
                    $firstNonEmptyLine = $trimmedLine;
                    break;
                }
            }
            
            $hasTryStart = strpos($firstNonEmptyLine, 'try {') !== false;
            $hasCorrectCatch = strpos($methodContent, 'return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR') !== false;
            
            $result = [
                'hasTryStart' => $hasTryStart,
                'hasCorrectCatch' => $hasCorrectCatch,
                'firstLine' => $firstNonEmptyLine
            ];
            
            // 记录问题
            if (!$hasTryStart) {
                $this->issuesFound[] = "控制器 {$fileName}, 方法 {$methodName}: 第一行未包含 'try {'";
            }
            
            if (!$hasCorrectCatch) {
                $this->issuesFound[] = "控制器 {$fileName}, 方法 {$methodName}: catch块中未包含正确的错误响应";
            }
            
            return $result;
        }
        
        $this->issuesFound[] = "控制器 {$fileName}, 方法 {$methodName}: 无法解析方法内容";
        return [
            'hasTryStart' => false,
            'hasCorrectCatch' => false,
            'firstLine' => '无法解析'
        ];
    }

    private function writeReportToFile()
    {
        $report = "# API控制器Try-Catch架构检测详细报告\n\n";
        $report .= "## 检测概要\n\n";
        $report .= "- **总控制器数量**: {$this->totalControllers}\n";
        $report .= "- **总方法数量**: {$this->totalMethods}\n";
        $report .= "- **发现问题数量**: " . count($this->issuesFound) . "\n\n";
        
        if (empty($this->issuesFound)) {
            $report .= "🎉 **所有控制器都符合try-catch架构要求！**\n\n";
        } else {
            $report .= "## ❌ 发现的问题\n\n";
            foreach ($this->issuesFound as $index => $issue) {
                $report .= ($index + 1) . ". {$issue}\n";
            }
            $report .= "\n";
        }
        
        $report .= "## 详细检测结果\n\n";
        
        foreach ($this->results as $fileName => $result) {
            $report .= "### {$fileName}\n\n";
            $report .= "- **Log导入**: " . ($result['hasLogImport'] ? '✅ 已导入' : '❌ 未导入') . "\n";
            $report .= "- **方法数量**: {$result['methodCount']}\n\n";
            
            if (!empty($result['methods'])) {
                $report .= "#### 方法检测详情\n\n";
                $report .= "| 方法名 | Try开始 | Catch错误响应 | 第一行内容 |\n";
                $report .= "|--------|---------|---------------|------------|\n";
                
                foreach ($result['methods'] as $methodName => $methodResult) {
                    $tryIcon = $methodResult['hasTryStart'] ? '✅' : '❌';
                    $catchIcon = $methodResult['hasCorrectCatch'] ? '✅' : '❌';
                    $firstLine = str_replace('|', '\\|', $methodResult['firstLine']);
                    $report .= "| {$methodName} | {$tryIcon} | {$catchIcon} | {$firstLine} |\n";
                }
                $report .= "\n";
            }
        }
        
        $report .= "## 统计汇总\n\n";
        $report .= "| 控制器 | 方法数量 | Log导入 |\n";
        $report .= "|--------|----------|---------|\n";
        
        foreach ($this->results as $fileName => $result) {
            $logIcon = $result['hasLogImport'] ? '✅' : '❌';
            $report .= "| {$fileName} | {$result['methodCount']} | {$logIcon} |\n";
        }
        
        file_put_contents($this->reportFile, $report);
    }
}

// 生成详细报告
$reporter = new DetailedTryCatchReporter();
$reporter->generateDetailedReport();

?>