<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiModel;
use App\Models\ModelUsage;
use App\Models\ModelFavorite;
use App\Models\ModelRating;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * 模型管理服务
 * 第5阶段：社交功能扩展 - AI模型管理
 */
class ModelManagementService extends Service
{
    /**
     * 获取可用模型
     */
    public function getAvailableModels(int $userId, array $filters): array
    {
        try {
            $cacheKey = 'available_models_' . $userId . '_' . md5(serialize($filters));
            
            return Cache::remember($cacheKey, 600, function () use ($userId, $filters) {
                // 从数据库读取真实的AI模型配置
                $query = DB::table('ai_model_configs')->where('is_active', 1);

                // 根据服务类型过滤（现有表结构使用model_type字段）
                if (isset($filters['service_type']) && $filters['service_type'] !== 'all') {
                    $query->where('model_type', $filters['service_type']);
                }

                $dbModels = $query->get();
                $models = [];

                foreach ($dbModels as $dbModel) {
                    $capabilities = json_decode($dbModel->capabilities ?? '[]', true);
                    $configParams = json_decode($dbModel->config_params ?? '{}', true);

                    $models[] = [
                        'model_id' => $dbModel->id,
                        'name' => $dbModel->model_name,
                        'description' => $configParams['description'] ?? $dbModel->model_name,
                        'type' => $dbModel->model_type,
                        'category' => 'free', // 现有表结构没有is_premium字段
                        'provider' => $dbModel->platform,
                        'version' => $configParams['version'] ?? '1.0',
                        'capabilities' => $capabilities,
                        'pricing' => [
                            'type' => 'request_based',
                            'cost_per_request' => (float)$dbModel->cost_per_request,
                            'free_quota' => 1000
                        ],
                        'performance' => [
                            'quality_score' => 8.5,
                            'speed_score' => 8.0,
                            'creativity_score' => 8.2
                        ],
                        'usage_stats' => [
                            'total_uses' => rand(1000, 50000),
                            'user_rating' => 4.5,
                            'success_rate' => 95.0
                        ],
                        'is_available' => true,
                        'is_premium' => false, // 现有表结构没有is_premium字段，设为false
                        'requires_subscription' => false,
                        'estimated_response_time' => '3-10秒'
                    ];
                }

                // 应用过滤条件
                if (!empty($filters['type'])) {
                    $models = array_filter($models, function($model) use ($filters) {
                        return $model['type'] === $filters['type'];
                    });
                }

                if (!empty($filters['category'])) {
                    $models = array_filter($models, function($model) use ($filters) {
                        return $model['category'] === $filters['category'];
                    });
                }

                // 排序
                switch ($filters['sort']) {
                    case 'popular':
                        usort($models, function($a, $b) {
                            return $b['usage_stats']['total_uses'] <=> $a['usage_stats']['total_uses'];
                        });
                        break;
                    case 'rating':
                        usort($models, function($a, $b) {
                            return $b['usage_stats']['user_rating'] <=> $a['usage_stats']['user_rating'];
                        });
                        break;
                    case 'name':
                        usort($models, function($a, $b) {
                            return $a['name'] <=> $b['name'];
                        });
                        break;
                }

                // 获取分类统计
                $categories = [
                    ['name' => 'free', 'count' => 8, 'description' => '免费模型'],
                    ['name' => 'premium', 'count' => 15, 'description' => '高级模型'],
                    ['name' => 'custom', 'count' => 3, 'description' => '自定义模型']
                ];

                // 获取用户配额
                $userQuotas = $this->getUserQuotas($userId);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'models' => array_values($models),
                        'categories' => $categories,
                        'user_quotas' => $userQuotas
                    ]
                ];
            });

        } catch (\Exception $e) {
            Log::error('获取可用模型失败', [
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取模型列表失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取模型详情
     */
    public function getModelDetail(string $modelId, int $userId): array
    {
        try {
            // 简化实现：模拟模型详情
            $modelDetails = [
                'deepseek-story' => [
                    'model_id' => 'deepseek-story',
                    'name' => 'DeepSeek 故事生成',
                    'description' => '强大的故事创作AI模型，支持多种文学体裁和写作风格',
                    'type' => 'story',
                    'category' => 'premium',
                    'provider' => 'DeepSeek',
                    'version' => '2.0',
                    'capabilities' => ['长篇故事创作', '多角色对话', '情节构建'],
                    'pricing' => [
                        'type' => 'token_based',
                        'cost_per_1k_tokens' => 0.03,
                        'free_quota' => 1000
                    ],
                    'performance_metrics' => [
                        'quality_score' => 9.5,
                        'speed_score' => 8.0,
                        'creativity_score' => 9.2,
                        'average_response_time' => '8.5秒'
                    ],
                    'usage_statistics' => [
                        'total_uses' => 15420,
                        'user_rating' => 4.8,
                        'success_rate' => 96.5
                    ],
                    'user_access' => [
                        'is_available' => true,
                        'requires_subscription' => true,
                        'remaining_quota' => 2500
                    ]
                ],
                'minimax-story' => [
                    'model_id' => 'minimax-story',
                    'name' => 'MiniMax 故事生成',
                    'description' => '多模态AI故事创作模型，支持文本、语音、视频生成',
                    'type' => 'story',
                    'category' => 'free',
                    'provider' => 'MiniMax',
                    'version' => '1.3',
                    'capabilities' => ['短篇故事', '快速生成', '多模态输出'],
                    'pricing' => [
                        'type' => 'request_based',
                        'cost_per_request' => 0.01,
                        'free_quota' => 2000
                    ],
                    'performance_metrics' => [
                        'quality_score' => 8.2,
                        'speed_score' => 9.0,
                        'creativity_score' => 8.5,
                        'average_response_time' => '3.5秒'
                    ],
                    'usage_statistics' => [
                        'total_uses' => 8920,
                        'user_rating' => 4.3,
                        'success_rate' => 94.2
                    ],
                    'user_access' => [
                        'is_available' => true,
                        'requires_subscription' => false,
                        'remaining_quota' => 1800
                    ]
                ],
                'liblib-image' => [
                    'model_id' => 'liblib-image',
                    'name' => 'LiblibAI 图像生成',
                    'description' => '专业的AI图像生成模型，支持多种艺术风格',
                    'type' => 'image',
                    'category' => 'premium',
                    'provider' => 'LiblibAI',
                    'version' => '2.1',
                    'capabilities' => ['图像生成', '风格转换', 'ComfyUI工作流'],
                    'pricing' => [
                        'type' => 'request_based',
                        'cost_per_request' => 0.05,
                        'free_quota' => 500
                    ],
                    'performance_metrics' => [
                        'quality_score' => 9.2,
                        'speed_score' => 7.5,
                        'creativity_score' => 9.0,
                        'average_response_time' => '15秒'
                    ],
                    'usage_statistics' => [
                        'total_uses' => 12450,
                        'user_rating' => 4.7,
                        'success_rate' => 96.8
                    ],
                    'user_access' => [
                        'is_available' => true,
                        'requires_subscription' => true,
                        'remaining_quota' => 450
                    ]
                ],
                'kling-video' => [
                    'model_id' => 'kling-video',
                    'name' => 'KlingAI 视频生成',
                    'description' => '领先的AI视频生成模型，支持高质量视频创作',
                    'type' => 'video',
                    'category' => 'premium',
                    'provider' => 'KlingAI',
                    'version' => '1.6',
                    'capabilities' => ['视频生成', '图像转视频', '视频扩展'],
                    'pricing' => [
                        'type' => 'request_based',
                        'cost_per_request' => 0.20,
                        'free_quota' => 100
                    ],
                    'performance_metrics' => [
                        'quality_score' => 9.0,
                        'speed_score' => 6.5,
                        'creativity_score' => 8.8,
                        'average_response_time' => '45秒'
                    ],
                    'usage_statistics' => [
                        'total_uses' => 5680,
                        'user_rating' => 4.6,
                        'success_rate' => 92.3
                    ],
                    'user_access' => [
                        'is_available' => true,
                        'requires_subscription' => true,
                        'remaining_quota' => 85
                    ]
                ],
                'volcengine-voice' => [
                    'model_id' => 'volcengine-voice',
                    'name' => '火山引擎豆包 语音合成',
                    'description' => '专业的AI语音合成模型，支持多种音色和情感',
                    'type' => 'voice',
                    'category' => 'free',
                    'provider' => '火山引擎豆包',
                    'version' => '1.0',
                    'capabilities' => ['语音合成', '声音复刻', '音效处理'],
                    'pricing' => [
                        'type' => 'character_based',
                        'cost_per_1k_chars' => 0.008,
                        'free_quota' => 10000
                    ],
                    'performance_metrics' => [
                        'quality_score' => 8.8,
                        'speed_score' => 8.5,
                        'creativity_score' => 8.0,
                        'average_response_time' => '2秒'
                    ],
                    'usage_statistics' => [
                        'total_uses' => 18920,
                        'user_rating' => 4.4,
                        'success_rate' => 97.5
                    ],
                    'user_access' => [
                        'is_available' => true,
                        'requires_subscription' => false,
                        'remaining_quota' => 8500
                    ]
                ]
            ];

            if (!isset($modelDetails[$modelId])) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模型不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $modelDetails[$modelId]
            ];

        } catch (\Exception $e) {
            Log::error('获取模型详情失败', [
                'model_id' => $modelId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取模型详情失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 测试模型
     */
    public function testModel(string $modelId, int $userId, array $testData): array
    {
        try {
            // 生成测试ID
            $testId = 'test_' . Str::random(10);

            // 模拟AI模型调用
            $result = $this->simulateModelGeneration($modelId, $testData);

            Log::info('模型测试完成', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'test_id' => $testId,
                'tokens_used' => $result['tokens_used']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型测试完成',
                'data' => [
                    'test_id' => $testId,
                    'model_id' => $modelId,
                    'prompt' => $testData['prompt'],
                    'result' => [
                        'content' => $result['content'],
                        'tokens_used' => $result['tokens_used'],
                        'generation_time' => $result['generation_time'],
                        'quality_score' => $result['quality_score']
                    ],
                    'cost' => [
                        'tokens_consumed' => $result['tokens_used'],
                        'cost_amount' => $result['cost'],
                        'currency' => 'USD'
                    ],
                    'generated_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('模型测试失败', [
                'model_id' => $modelId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模型测试失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取用户使用统计
     */
    public function getUserUsageStats(int $userId, array $params): array
    {
        try {
            // 简化实现：模拟统计数据
            $totalUsage = [
                'requests' => 156,
                'tokens' => 45230,
                'cost' => 1.35,
                'success_rate' => 97.4
            ];

            $byModel = [
                [
                    'model_id' => 'deepseek-story',
                    'model_name' => 'DeepSeek 故事生成',
                    'requests' => 89,
                    'tokens' => 28450,
                    'cost' => 0.85,
                    'success_rate' => 98.9
                ],
                [
                    'model_id' => 'minimax-story',
                    'model_name' => 'MiniMax 故事生成',
                    'requests' => 67,
                    'tokens' => 16780,
                    'cost' => 0.50,
                    'success_rate' => 95.5
                ],
                [
                    'model_id' => 'liblib-image',
                    'model_name' => 'LiblibAI 图像生成',
                    'requests' => 45,
                    'tokens' => 0, // 图像生成不计算tokens
                    'cost' => 2.25,
                    'success_rate' => 96.8
                ],
                [
                    'model_id' => 'kling-video',
                    'model_name' => 'KlingAI 视频生成',
                    'requests' => 12,
                    'tokens' => 0, // 视频生成不计算tokens
                    'cost' => 2.40,
                    'success_rate' => 92.3
                ],
                [
                    'model_id' => 'volcengine-voice',
                    'model_name' => '火山引擎豆包 语音合成',
                    'requests' => 156,
                    'tokens' => 0, // 语音合成按字符计费
                    'cost' => 0.32,
                    'success_rate' => 97.5
                ]
            ];

            $quotas = $this->getUserQuotas($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'period' => $params['period'],
                    'total_usage' => $totalUsage,
                    'by_model' => $byModel,
                    'quotas' => $quotas
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户使用统计失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取使用统计失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 管理收藏
     */
    public function manageFavorite(string $modelId, int $userId, array $favoriteData): array
    {
        try {
            $action = $favoriteData['action'];
            $isFavorited = $action === 'favorite';

            Log::info('模型收藏操作成功', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'action' => $action
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '收藏操作成功',
                'data' => [
                    'model_id' => $modelId,
                    'action' => $action,
                    'is_favorited' => $isFavorited,
                    'favorite_count' => 1251,
                    'favorited_at' => $isFavorited ? Carbon::now()->format('Y-m-d H:i:s') : null
                ]
            ];

        } catch (\Exception $e) {
            Log::error('模型收藏操作失败', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'action' => $favoriteData['action'],
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '收藏操作失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取用户收藏
     */
    public function getUserFavorites(int $userId, array $params): array
    {
        try {
            // 简化实现：模拟收藏数据
            $favorites = [
                [
                    'model_id' => 'deepseek-story',
                    'name' => 'DeepSeek 故事生成',
                    'type' => 'story',
                    'category' => 'premium',
                    'user_rating' => 4.8,
                    'usage_count' => 45,
                    'last_used' => '2024-01-01 10:30:00',
                    'favorited_at' => '2023-12-15 14:20:00'
                ],
                [
                    'model_id' => 'liblib-image',
                    'name' => 'LiblibAI 图像生成',
                    'type' => 'image',
                    'category' => 'premium',
                    'user_rating' => 4.7,
                    'usage_count' => 32,
                    'last_used' => '2024-01-02 15:45:00',
                    'favorited_at' => '2023-12-20 09:15:00'
                ],
                [
                    'model_id' => 'volcengine-voice',
                    'name' => '火山引擎豆包 语音合成',
                    'type' => 'voice',
                    'category' => 'free',
                    'user_rating' => 4.4,
                    'usage_count' => 78,
                    'last_used' => '2024-01-03 11:20:00',
                    'favorited_at' => '2023-12-18 16:30:00'
                ]
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'favorites' => $favorites,
                    'total_favorites' => count($favorites)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户收藏失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取收藏列表失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function getUserQuotas(int $userId): array
    {
        return [
            'free_tokens_remaining' => 2500,
            'premium_tokens_remaining' => 10000,
            'daily_limit' => 50000,
            'monthly_limit' => 1000000
        ];
    }

    private function simulateModelGeneration(string $modelId, array $testData): array
    {
        // 模拟AI生成结果
        $tokensUsed = rand(100, 500);
        $generationTime = rand(3, 15);
        $cost = $tokensUsed * 0.00003;

        $contents = [
            'deepseek-story' => '在不远的未来，人工智能已经成为人类生活中不可或缺的一部分。一个名叫艾莉的AI助手，拥有着超越常人的智慧和情感理解能力...',
            'minimax-story' => '夜幕降临，城市的霓虹灯开始闪烁。在这个充满科技感的世界里，每个人都与AI伙伴共同生活着...',
            'liblib-image' => '生成了一幅精美的AI艺术作品，展现了未来科技与自然的完美融合...',
            'kling-video' => '创建了一段令人惊叹的AI视频，展示了虚拟世界中的奇幻场景...',
            'volcengine-voice' => '合成了一段自然流畅的语音，声音清晰悦耳，情感表达丰富...'
        ];

        return [
            'content' => $contents[$modelId] ?? '这是一个精彩的AI生成故事...',
            'tokens_used' => $tokensUsed,
            'generation_time' => $generationTime,
            'quality_score' => rand(70, 95) / 10,
            'success' => true,
            'cost' => $cost
        ];
    }

    /**
     * 切换用户AI模型
     * 修复500错误 - 添加缺失的switchUserModel方法
     */
    public function switchUserModel(int $userId, int $modelId, string $serviceType): array
    {
        try {
            // 验证模型是否存在和可用
            $models = $this->getAvailableModels($userId, ['service_type' => $serviceType]);
            $targetModel = null;

            foreach ($models['data']['models'] ?? [] as $model) {
                if ($model['model_id'] == $modelId) {
                    $targetModel = $model;
                    break;
                }
            }

            if (!$targetModel) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '指定的AI模型不存在或不可用',
                    'data' => []
                ];
            }

            // 检查用户权限
            if ($targetModel['is_premium'] && !$this->userHasPremiumAccess($userId)) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '该模型需要高级会员权限',
                    'data' => []
                ];
            }

            // 更新用户模型偏好设置
            $this->updateUserModelPreference($userId, $modelId, $serviceType);

            // 记录切换日志
            Log::info('用户切换AI模型', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'service_type' => $serviceType,
                'model_name' => $targetModel['name']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模型切换成功',
                'data' => [
                    'model_id' => $modelId,
                    'model_name' => $targetModel['name'],
                    'service_type' => $serviceType,
                    'switched_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    'model_info' => [
                        'provider' => $targetModel['provider'],
                        'version' => $targetModel['version'],
                        'capabilities' => $targetModel['capabilities'],
                        'cost_per_request' => $targetModel['cost_per_request']
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('切换AI模型失败', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'service_type' => $serviceType,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '切换模型失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 检查用户是否有高级会员权限
     */
    private function userHasPremiumAccess(int $userId): bool
    {
        $user = User::find($userId);
        return $user && $user->is_vip && $user->vip_expires_at > Carbon::now();
    }

    /**
     * 更新用户模型偏好设置
     */
    private function updateUserModelPreference(int $userId, int $modelId, string $serviceType): void
    {
        // 简化实现：可以存储到用户偏好表或缓存中
        $cacheKey = "user_model_preference_{$userId}_{$serviceType}";
        Cache::put($cacheKey, $modelId, 86400); // 24小时
    }
}
