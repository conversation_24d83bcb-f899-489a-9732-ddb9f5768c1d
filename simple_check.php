<?php
/**
 * CogniAud 简化版控制器try-catch架构检测脚本
 */

class SimpleTryCatchChecker
{
    private $controllersPath;
    private $results = [];

    public function __construct($controllersPath)
    {
        $this->controllersPath = $controllersPath;
    }

    public function runCheck()
    {
        echo "🔍 CogniAud 控制器try-catch架构检测\n";
        echo "检测路径: {$this->controllersPath}\n";
        echo str_repeat("=", 80) . "\n";

        $controllers = $this->getControllerFiles();
        
        $totalControllers = 0;
        $totalMethods = 0;
        $methodsWithTryCatch = 0;
        $controllersWithLog = 0;
        $validCatchBlocks = 0;

        foreach ($controllers as $controller) {
            $fileName = basename($controller);
            $content = file_get_contents($controller);
            
            // 检查Log导入
            $hasLogImport = preg_match('/use\s+Illuminate\\\\Support\\\\Facades\\\\Log;/', $content);
            
            // 查找所有public function方法
            preg_match_all('/public\s+function\s+(\w+)\s*\([^)]*\)/', $content, $matches);
            $methods = array_filter($matches[1], function($method) {
                return $method !== '__construct';
            });
            
            $methodsWithTry = 0;
            $validCatch = 0;
            
            foreach ($methods as $method) {
                // 查找方法体
                $pattern = '/public\s+function\s+' . preg_quote($method) . '\s*\([^)]*\)\s*\{(.*?)\n\s*\}/s';
                if (preg_match($pattern, $content, $methodMatch)) {
                    $methodBody = $methodMatch[1];
                    
                    // 检查是否有try-catch
                    $hasTry = preg_match('/try\s*\{/', $methodBody);
                    $hasCatch = preg_match('/catch\s*\([^)]*\)\s*\{/', $methodBody);
                    
                    if ($hasTry && $hasCatch) {
                        $methodsWithTry++;
                        
                        // 检查catch块内容
                        if (preg_match('/catch\s*\([^)]*\)\s*\{(.*?)\}/s', $methodBody, $catchMatch)) {
                            $catchContent = $catchMatch[1];
                            
                            $hasLogError = strpos($catchContent, 'Log::error') !== false;
                            $hasMethod = strpos($catchContent, '__METHOD__') !== false;
                            $hasUserId = strpos($catchContent, 'user_id') !== false;
                            $hasRequestData = strpos($catchContent, 'request_data') !== false;
                            $hasErrorMsg = strpos($catchContent, '$e->getMessage()') !== false;
                            $hasTrace = strpos($catchContent, '$e->getTraceAsString()') !== false;
                            $hasErrorResponse = strpos($catchContent, 'errorResponse') !== false && 
                                              strpos($catchContent, 'ApiCodeEnum::CONTROLLER_ERROR') !== false &&
                                              strpos($catchContent, '[]') !== false;
                            
                            if ($hasLogError && $hasMethod && $hasUserId && $hasRequestData && 
                                $hasErrorMsg && $hasTrace && $hasErrorResponse) {
                                $validCatch++;
                            }
                        }
                    }
                }
            }
            
            $totalControllers++;
            $totalMethods += count($methods);
            $methodsWithTryCatch += $methodsWithTry;
            $validCatchBlocks += $validCatch;
            
            if ($hasLogImport) {
                $controllersWithLog++;
            }
            
            // 输出结果
            $logStatus = $hasLogImport ? '✅' : '❌';
            echo "\n📁 {$fileName}:\n";
            echo "  {$logStatus} Log导入: " . ($hasLogImport ? '已导入' : '未导入') . "\n";
            echo "  📊 方法统计: 总计" . count($methods) . "个, try-catch改造{$methodsWithTry}个, 有效catch块{$validCatch}个\n";
            
            if ($methodsWithTry < count($methods) || $validCatch < $methodsWithTry) {
                echo "  ⚠️  需要完善的方法:\n";
                foreach ($methods as $method) {
                    $pattern = '/public\s+function\s+' . preg_quote($method) . '\s*\([^)]*\)\s*\{(.*?)\n\s*\}/s';
                    if (preg_match($pattern, $content, $methodMatch)) {
                        $methodBody = $methodMatch[1];
                        $hasTry = preg_match('/try\s*\{/', $methodBody);
                        $hasCatch = preg_match('/catch\s*\([^)]*\)\s*\{/', $methodBody);
                        
                        if (!$hasTry || !$hasCatch) {
                            echo "    ❌ {$method}: 缺少try-catch\n";
                        } else {
                            // 检查catch块质量
                            if (preg_match('/catch\s*\([^)]*\)\s*\{(.*?)\}/s', $methodBody, $catchMatch)) {
                                $catchContent = $catchMatch[1];
                                $issues = [];
                                
                                if (strpos($catchContent, 'Log::error') === false) $issues[] = '缺少Log::error';
                                if (strpos($catchContent, '__METHOD__') === false) $issues[] = '缺少__METHOD__';
                                if (strpos($catchContent, 'user_id') === false) $issues[] = '缺少user_id';
                                if (strpos($catchContent, 'request_data') === false) $issues[] = '缺少request_data';
                                if (strpos($catchContent, '$e->getMessage()') === false) $issues[] = '缺少error message';
                                if (strpos($catchContent, '$e->getTraceAsString()') === false) $issues[] = '缺少trace';
                                if (strpos($catchContent, 'errorResponse') === false || 
                                    strpos($catchContent, 'ApiCodeEnum::CONTROLLER_ERROR') === false ||
                                    strpos($catchContent, '[]') === false) {
                                    $issues[] = '缺少标准errorResponse';
                                }
                                
                                if (!empty($issues)) {
                                    echo "    ⚠️  {$method}: " . implode(', ', $issues) . "\n";
                                }
                            }
                        }
                    }
                }
            }
        }
        
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "🎯 检测总结:\n";
        echo "  总控制器数: {$totalControllers}\n";
        echo "  总方法数: {$totalMethods}\n";
        echo "  已改造方法数: {$methodsWithTryCatch}\n";
        echo "  有效catch块数: {$validCatchBlocks}\n";
        echo "  Log导入完成: {$controllersWithLog}\n";
        
        $methodRate = $totalMethods > 0 ? round(($methodsWithTryCatch / $totalMethods) * 100, 2) : 0;
        $catchRate = $methodsWithTryCatch > 0 ? round(($validCatchBlocks / $methodsWithTryCatch) * 100, 2) : 0;
        $logRate = $totalControllers > 0 ? round(($controllersWithLog / $totalControllers) * 100, 2) : 0;
        
        echo "\n📈 完成率:\n";
        echo "  方法try-catch改造完成率: {$methodRate}%\n";
        echo "  catch块标准化完成率: {$catchRate}%\n";
        echo "  Log导入完成率: {$logRate}%\n";
        echo str_repeat("=", 80) . "\n";
    }

    private function getControllerFiles()
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->controllersPath)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }

        sort($files);
        return $files;
    }
}

// 执行检测
$checker = new SimpleTryCatchChecker('D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Http\Controllers\Api');
$checker->runCheck();
