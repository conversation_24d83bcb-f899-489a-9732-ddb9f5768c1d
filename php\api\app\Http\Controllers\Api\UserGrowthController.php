<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\UserGrowthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 用户成长系统与等级管理
 */
class UserGrowthController extends Controller
{
    protected $growthService;

    public function __construct(UserGrowthService $growthService)
    {
        $this->growthService = $growthService;
    }

    /**
     * @ApiTitle(获取用户成长信息)
     * @ApiSummary(获取用户的等级、经验、成就等成长信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user-growth/profile)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "user_id": 123,
     *     "level": 15,
     *     "experience": 2850,
     *     "experience_to_next_level": 150,
     *     "total_experience_for_next_level": 3000,
     *     "level_progress": 95.0,
     *     "title": "创作大师",
     *     "badges": [
     *       {
     *         "badge_id": 1,
     *         "name": "故事新手",
     *         "description": "创作第一个故事",
     *         "icon": "https://example.com/badge1.png",
     *         "earned_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "achievements": [
     *       {
     *         "achievement_id": 1,
     *         "name": "连续创作者",
     *         "description": "连续7天创作内容",
     *         "progress": 7,
     *         "target": 7,
     *         "completed": true,
     *         "reward_experience": 100
     *       }
     *     ],
     *     "statistics": {
     *       "total_creations": 45,
     *       "total_likes": 234,
     *       "total_followers": 56,
     *       "creation_streak": 7,
     *       "most_popular_type": "story"
     *     }
     *   }
     * })
     */
    public function profile(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->growthService->getUserGrowthProfile($user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取用户成长信息失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取用户成长信息失败', []);
        }
    }

    /**
     * @ApiTitle(获取排行榜)
     * @ApiSummary(获取用户排行榜信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user-growth/leaderboard)
     * @ApiParams(name="type", type="string", required=false, description="排行榜类型：level/experience/creations/likes")
     * @ApiParams(name="period", type="string", required=false, description="时间周期：week/month/all")
     * @ApiParams(name="limit", type="int", required=false, description="返回数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "leaderboard_type": "level",
     *     "period": "month",
     *     "my_rank": 15,
     *     "rankings": [
     *       {
     *         "rank": 1,
     *         "user_id": 456,
     *         "username": "创作达人",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "level": 25,
     *         "experience": 5000,
     *         "value": 5000,
     *         "badge": "创作大师"
     *       }
     *     ],
     *     "total_participants": 1250,
     *     "updated_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function leaderboard(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:level,experience,creations,likes,followers',
                'period' => 'sometimes|string|in:week,month,all',
                'limit' => 'sometimes|integer|min:1|max:100'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];

            $params = [
                'type' => $request->get('type', 'level'),
                'period' => $request->get('period', 'month'),
                'limit' => $request->get('limit', 50)
            ];

            $result = $this->growthService->getLeaderboard($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取排行榜失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取排行榜失败', []);
        }
    }

    /**
     * @ApiTitle(完成成就)
     * @ApiSummary(手动触发成就完成检查)
     * @ApiMethod(POST)
     * @ApiRoute(/api/user-growth/complete-achievement)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="achievement_id", type="int", required=true, description="成就ID")
     * @ApiParams(name="progress_data", type="object", required=false, description="进度数据")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "成就完成",
     *   "data": {
     *     "achievement_id": 1,
     *     "name": "连续创作者",
     *     "description": "连续7天创作内容",
     *     "reward_experience": 100,
     *     "reward_badge": {
     *       "badge_id": 5,
     *       "name": "坚持不懈",
     *       "icon": "https://example.com/badge5.png"
     *     },
     *     "completed_at": "2024-01-01 12:00:00",
     *     "level_up": false,
     *     "new_level": 15,
     *     "total_experience": 2950
     *   }
     * })
     */
    public function completeAchievement(Request $request)
    {
        try {
            $rules = [
                'achievement_id' => 'required|integer|exists:achievements,id',
                'progress_data' => 'sometimes|array'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];

            $achievementData = [
                'achievement_id' => $request->achievement_id,
                'progress_data' => $request->get('progress_data', [])
            ];

            $result = $this->growthService->completeAchievement($user->id, $achievementData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('完成成就失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '完成成就失败', []);
        }
    }

    /**
     * @ApiTitle(获取每日任务)
     * @ApiSummary(获取用户的每日任务列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user-growth/daily-tasks)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "date": "2024-01-01",
     *     "tasks": [
     *       {
     *         "task_id": 1,
     *         "name": "创作一个故事",
     *         "description": "今天创作一个新的故事",
     *         "type": "creation",
     *         "target": 1,
     *         "progress": 0,
     *         "completed": false,
     *         "reward_experience": 50,
     *         "expires_at": "2024-01-02 00:00:00"
     *       }
     *     ],
     *     "completion_stats": {
     *       "completed_tasks": 2,
     *       "total_tasks": 5,
     *       "completion_rate": 40.0,
     *       "streak_days": 3,
     *       "total_experience_today": 150
     *     }
     *   }
     * })
     */
    public function dailyTasks(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->growthService->getDailyTasks($user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取每日任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取每日任务失败', []);
        }
    }

    /**
     * @ApiTitle(完成每日任务)
     * @ApiSummary(标记每日任务为完成)
     * @ApiMethod(POST)
     * @ApiRoute(/api/user-growth/complete-daily-task)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="task_id", type="int", required=true, description="任务ID")
     * @ApiParams(name="completion_data", type="object", required=false, description="完成数据")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "每日任务完成",
     *   "data": {
     *     "task_id": 1,
     *     "name": "创作一个故事",
     *     "reward_experience": 50,
     *     "completed_at": "2024-01-01 15:30:00",
     *     "bonus_reward": {
     *       "type": "streak_bonus",
     *       "experience": 10,
     *       "description": "连续完成任务奖励"
     *     },
     *     "level_up": false,
     *     "new_level": 15,
     *     "total_experience": 2900
     *   }
     * })
     */
    public function completeDailyTask(Request $request)
    {
        try {
            $rules = [
                'task_id' => 'required|integer|exists:daily_tasks,id',
                'completion_data' => 'sometimes|array'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];

            $taskData = [
                'task_id' => $request->task_id,
                'completion_data' => $request->get('completion_data', [])
            ];

            $result = $this->growthService->completeDailyTask($user->id, $taskData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('完成每日任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '完成每日任务失败', []);
        }
    }

    /**
     * @ApiTitle(获取成长历史)
     * @ApiSummary(获取用户的成长历史记录)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user-growth/history)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="历史类型：level_up/achievement/badge/task")
     * @ApiParams(name="date_from", type="string", required=false, description="开始日期")
     * @ApiParams(name="date_to", type="string", required=false, description="结束日期")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "history": [
     *       {
     *         "id": 123,
     *         "type": "level_up",
     *         "title": "等级提升",
     *         "description": "恭喜你升级到15级！",
     *         "data": {
     *           "old_level": 14,
     *           "new_level": 15,
     *           "experience_gained": 100
     *         },
     *         "created_at": "2024-01-01 12:00:00"
     *       }
     *     ],
     *     "statistics": {
     *       "total_level_ups": 15,
     *       "total_achievements": 8,
     *       "total_badges": 12,
     *       "total_tasks_completed": 156
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 45,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function history(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:level_up,achievement,badge,task',
                'date_from' => 'sometimes|date_format:Y-m-d',
                'date_to' => 'sometimes|date_format:Y-m-d|after_or_equal:date_from',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败', []);
            }

            $user = $authResult['user'];

            $filters = [
                'type' => $request->get('type'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->growthService->getGrowthHistory($user->id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取成长历史失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取成长历史失败', []);
        }
    }

    /**
     * @ApiTitle(获取成长统计)
     * @ApiSummary(获取用户成长的统计分析)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user-growth/statistics)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="period", type="string", required=false, description="统计周期：week/month/year")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "month",
     *     "experience_gained": 850,
     *     "levels_gained": 2,
     *     "achievements_completed": 3,
     *     "badges_earned": 2,
     *     "daily_tasks_completed": 28,
     *     "creation_count": 15,
     *     "interaction_count": 89,
     *     "growth_trends": [
     *       {"date": "2024-01-01", "experience": 50, "level": 14},
     *       {"date": "2024-01-02", "experience": 75, "level": 14}
     *     ],
     *     "activity_heatmap": [
     *       {"date": "2024-01-01", "activity_score": 8},
     *       {"date": "2024-01-02", "activity_score": 6}
     *     ],
     *     "comparison": {
     *       "previous_period": {
     *         "experience_gained": 650,
     *         "growth_rate": 30.8
     *       },
     *       "average_user": {
     *         "experience_gained": 420,
     *         "percentile": 85
     *       }
     *     }
     *   }
     * })
     */
    public function statistics(Request $request)
    {
        try {
            $rules = [
                'period' => 'sometimes|string|in:week,month,year'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $params = [
                'period' => $request->get('period', 'month')
            ];

            $result = $this->growthService->getGrowthStatistics($user->id, $params);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取成长统计失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取成长统计失败', []);
        }
    }

    /**
     * @ApiTitle(设置成长目标)
     * @ApiSummary(设置用户的成长目标)
     * @ApiMethod(POST)
     * @ApiRoute(/api/user-growth/set-goals)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="goals", type="array", required=true, description="目标数组")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "成长目标设置成功",
     *   "data": {
     *     "goals": [
     *       {
     *         "goal_id": 1,
     *         "type": "level",
     *         "target": 20,
     *         "current": 15,
     *         "deadline": "2024-02-01",
     *         "progress": 75.0
     *       }
     *     ],
     *     "total_goals": 3,
     *     "achievable_goals": 2,
     *     "estimated_completion": "2024-01-25"
     *   }
     * })
     */
    public function setGoals(Request $request)
    {
        try {
            $rules = [
                'goals' => 'required|array|min:1|max:5',
                'goals.*.type' => 'required|string|in:level,experience,creations,achievements',
                'goals.*.target' => 'required|integer|min:1',
                'goals.*.deadline' => 'required|date|after:today'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->growthService->setUserGoals($user->id, $request->goals);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('设置成长目标失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '设置成长目标失败', []);
        }
    }

    /**
     * @ApiTitle(获取成长建议)
     * @ApiSummary(获取个性化的成长建议)
     * @ApiMethod(GET)
     * @ApiRoute(/api/user-growth/recommendations)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "recommendations": [
     *       {
     *         "type": "achievement",
     *         "title": "完成"连续创作者"成就",
     *         "description": "再创作3天即可完成此成就",
     *         "priority": "high",
     *         "reward": "100经验值 + 徽章",
     *         "action": "继续每日创作"
     *       }
     *     ],
     *     "focus_areas": [
     *       {
     *         "area": "创作频率",
     *         "current_score": 7,
     *         "target_score": 9,
     *         "suggestions": ["设置每日创作提醒", "参与创作挑战"]
     *       }
     *     ],
     *     "next_milestones": [
     *       {
     *         "milestone": "升级到16级",
     *         "progress": 95.0,
     *         "estimated_time": "2天",
     *         "required_actions": ["完成2个每日任务"]
     *       }
     *     ]
     *   }
     * })
     */
    public function recommendations(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->growthService->getGrowthRecommendations($user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取成长建议失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取成长建议失败', []);
        }
    }

    /**
     * 获取用户成长里程碑
     * 修复500错误 - 添加缺失的milestones方法
     *
     * @param Request $request
     * @return array
     */
    public function milestones(Request $request)
    {
        try {
            // 验证用户身份
            $authResult = AuthService::authenticate($request);

            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 获取用户成长里程碑
            $result = $this->growthService->getUserMilestones($user->id);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $result
            ];

        } catch (\Exception $e) {
            Log::error('获取成长里程碑失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取成长里程碑失败', []);
        }
    }
}
