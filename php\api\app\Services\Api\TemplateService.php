<?php

namespace App\Services\Api;

use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\Template;
use App\Models\Project;
use App\Models\Resource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 模板服务
 * 第4阶段：模板管理系统
 */
class TemplateService
{
    /**
     * 创建模板
     */
    public function createTemplate(int $userId, array $templateData): array
    {
        try {
            DB::beginTransaction();

            // 验证来源资源/项目
            $source = $this->validateSource($templateData['source_type'], $templateData['source_id'], $userId);
            if (!$source) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '来源资源或项目不存在',
                    'data' => []
                ];
            }

            // 提取模板配置
            $configuration = $this->extractConfiguration($source, $templateData);

            // 创建模板
            $template = Template::create([
                'user_id' => $userId,
                'name' => $templateData['name'],
                'description' => $templateData['description'],
                'type' => $templateData['type'],
                'category' => $templateData['category'],
                'source_type' => $templateData['source_type'],
                'source_id' => $templateData['source_id'],
                'visibility' => $templateData['visibility'],
                'tags' => $templateData['tags'],
                'configuration' => $configuration,
                'status' => Template::STATUS_ACTIVE,
                'metadata' => [
                    'created_by' => 'template_service',
                    'source_name' => $source->name ?? $source->title ?? 'Unknown'
                ]
            ]);

            DB::commit();

            Log::info('模板创建成功', [
                'template_id' => $template->id,
                'user_id' => $userId,
                'name' => $templateData['name'],
                'type' => $templateData['type']
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模板创建成功',
                'data' => [
                    'template_id' => $template->id,
                    'name' => $template->name,
                    'type' => $template->type,
                    'category' => $template->category,
                    'visibility' => $template->visibility,
                    'author_id' => $template->user_id,
                    'usage_count' => 0,
                    'rating' => 0,
                    'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                    'preview_url' => route('api.templates.preview', $template->id)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('模板创建失败', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::TEMPLATE_ADD_FAILED,
                'message' => '模板创建失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 使用模板
     */
    public function useTemplate(int $templateId, int $userId, array $useData): array
    {
        try {
            DB::beginTransaction();

            $template = Template::where('id', $templateId)
                ->where(function ($query) use ($userId) {
                    $query->where('visibility', Template::VISIBILITY_PUBLIC)
                          ->orWhere('user_id', $userId);
                })
                ->first();

            if (!$template) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模板不存在或无权限使用',
                    'data' => []
                ];
            }

            // 根据模板类型创建相应的对象
            $created = $this->createFromTemplate($template, $userId, $useData);

            if (!$created) {
                return [
                    'code' => ApiCodeEnum::TEMPLATE_ADD_FAILED,
                    'message' => '基于模板创建失败',
                    'data' => []
                ];
            }

            // 增加使用次数
            $template->increment('usage_count');

            DB::commit();

            Log::info('模板使用成功', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'created_type' => $template->type,
                'created_id' => $created->id
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模板应用成功',
                'data' => [
                    'template_id' => $templateId,
                    'created_type' => $template->type,
                    'created_id' => $created->id,
                    'name' => $useData['name'],
                    'applied_settings' => $useData['apply_settings'],
                    'customizations_applied' => count($useData['customization']),
                    'created_at' => $created->created_at->format('Y-m-d H:i:s'),
                    'redirect_url' => $this->getRedirectUrl($template->type, $created->id)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('模板使用失败', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模板使用失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取模板市场
     */
    public function getTemplateMarketplace(array $filters): array
    {
        try {
            $cacheKey = 'template_marketplace_' . md5(serialize($filters));
            
            return Cache::remember($cacheKey, 300, function () use ($filters) {
                $query = Template::where('visibility', Template::VISIBILITY_PUBLIC)
                    ->where('status', Template::STATUS_ACTIVE)
                    ->with(['user:id,username,avatar']);

                // 应用过滤条件
                if (!empty($filters['category'])) {
                    $query->where('category', $filters['category']);
                }

                if (!empty($filters['type'])) {
                    $query->where('type', $filters['type']);
                }

                if (!empty($filters['tags'])) {
                    $tags = explode(',', $filters['tags']);
                    $query->where(function ($q) use ($tags) {
                        foreach ($tags as $tag) {
                            $q->orWhereJsonContains('tags', trim($tag));
                        }
                    });
                }

                if (!empty($filters['search'])) {
                    $search = $filters['search'];
                    $query->where(function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%")
                          ->orWhere('description', 'like', "%{$search}%");
                    });
                }

                // 排序
                switch ($filters['sort']) {
                    case 'popular':
                        $query->orderBy('usage_count', 'desc');
                        break;
                    case 'rating':
                        $query->orderBy('rating', 'desc');
                        break;
                    case 'featured':
                        $query->where('featured', true)->orderBy('created_at', 'desc');
                        break;
                    default:
                        $query->orderBy('created_at', 'desc');
                }

                // 分页
                $perPage = $filters['per_page'] ?? 20;
                $page = $filters['page'] ?? 1;

                $templates = $query->paginate($perPage, ['*'], 'page', $page);

                $templateList = [];
                foreach ($templates->items() as $template) {
                    $templateList[] = [
                        'template_id' => $template->id,
                        'name' => $template->name,
                        'description' => $template->description,
                        'type' => $template->type,
                        'category' => $template->category,
                        'tags' => $template->tags,
                        'author' => [
                            'user_id' => $template->user->id,
                            'username' => $template->user->username,
                            'avatar' => $template->user->avatar
                        ],
                        'usage_count' => $template->usage_count,
                        'rating' => $template->rating,
                        'review_count' => $template->review_count,
                        'preview_url' => route('api.templates.preview', $template->id),
                        'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                        'updated_at' => $template->updated_at->format('Y-m-d H:i:s')
                    ];
                }

                // 获取精选模板
                $featuredTemplates = $this->getFeaturedTemplates();

                // 获取分类统计
                $categories = $this->getCategoryStats();

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'templates' => $templateList,
                        'featured_templates' => $featuredTemplates,
                        'categories' => $categories,
                        'pagination' => [
                            'current_page' => $templates->currentPage(),
                            'per_page' => $templates->perPage(),
                            'total' => $templates->total(),
                            'last_page' => $templates->lastPage()
                        ]
                    ]
                ];
            });

        } catch (\Exception $e) {
            Log::error('获取模板市场失败', [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取模板市场失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取用户模板
     */
    public function getUserTemplates(int $userId, array $filters): array
    {
        try {
            $query = Template::where('user_id', $userId);

            // 应用过滤条件
            if (!empty($filters['type'])) {
                $query->where('type', $filters['type']);
            }

            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }

            if (!empty($filters['visibility'])) {
                $query->where('visibility', $filters['visibility']);
            }

            // 分页
            $perPage = $filters['per_page'] ?? 20;
            $page = $filters['page'] ?? 1;

            $templates = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $templateList = [];
            foreach ($templates->items() as $template) {
                $templateList[] = [
                    'template_id' => $template->id,
                    'name' => $template->name,
                    'type' => $template->type,
                    'category' => $template->category,
                    'visibility' => $template->visibility,
                    'usage_count' => $template->usage_count,
                    'rating' => $template->rating,
                    'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $template->updated_at->format('Y-m-d H:i:s')
                ];
            }

            // 计算统计信息
            $statistics = $this->calculateUserTemplateStatistics($userId);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'templates' => $templateList,
                    'statistics' => $statistics,
                    'pagination' => [
                        'current_page' => $templates->currentPage(),
                        'per_page' => $templates->perPage(),
                        'total' => $templates->total(),
                        'last_page' => $templates->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户模板失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取用户模板失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取模板详情
     */
    public function getTemplateDetail(int $templateId, ?int $userId = null): array
    {
        try {
            $template = Template::where('id', $templateId)
                ->where(function ($query) use ($userId) {
                    $query->where('visibility', Template::VISIBILITY_PUBLIC);
                    if ($userId) {
                        $query->orWhere('user_id', $userId);
                    }
                })
                ->with(['user:id,username,avatar'])
                ->first();

            if (!$template) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模板不存在或无权限访问',
                    'data' => []
                ];
            }

            // 增加浏览量（简化实现）
            $template->increment('view_count');

            $data = [
                'template_id' => $template->id,
                'name' => $template->name,
                'description' => $template->description,
                'type' => $template->type,
                'category' => $template->category,
                'visibility' => $template->visibility,
                'tags' => $template->tags,
                'author' => [
                    'user_id' => $template->user->id,
                    'username' => $template->user->username,
                    'avatar' => $template->user->avatar,
                    'template_count' => Template::where('user_id', $template->user->id)->count()
                ],
                'configuration' => $template->configuration,
                'statistics' => [
                    'usage_count' => $template->usage_count,
                    'rating' => $template->rating,
                    'review_count' => $template->review_count,
                    'favorite_count' => $template->favorite_count ?? 0,
                    'download_count' => $template->download_count ?? 0
                ],
                'preview' => [
                    'images' => $template->preview_images ?? [],
                    'demo_url' => $template->demo_url
                ],
                'reviews' => $this->getTemplateReviews($templateId),
                'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $template->updated_at->format('Y-m-d H:i:s')
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('获取模板详情失败', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取模板详情失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 更新模板
     */
    public function updateTemplate(int $templateId, int $userId, array $updateData): array
    {
        try {
            $template = Template::where('id', $templateId)
                ->where('user_id', $userId)
                ->first();

            if (!$template) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模板不存在或无权限修改',
                    'data' => []
                ];
            }

            $template->update($updateData);

            Log::info('模板更新成功', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'updated_fields' => array_keys($updateData)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模板更新成功',
                'data' => [
                    'template_id' => $template->id,
                    'name' => $template->name,
                    'description' => $template->description,
                    'tags' => $template->tags,
                    'visibility' => $template->visibility,
                    'updated_at' => $template->updated_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('模板更新失败', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模板更新失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 删除模板
     */
    public function deleteTemplate(int $templateId, int $userId): array
    {
        try {
            $template = Template::where('id', $templateId)
                ->where('user_id', $userId)
                ->first();

            if (!$template) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '模板不存在或无权限删除',
                    'data' => []
                ];
            }

            $template->delete();

            Log::info('模板删除成功', [
                'template_id' => $templateId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '模板删除成功',
                'data' => [
                    'template_id' => $templateId,
                    'deleted_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('模板删除失败', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '模板删除失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    // 私有辅助方法
    private function validateSource(string $sourceType, int $sourceId, int $userId)
    {
        switch ($sourceType) {
            case 'project':
                return Project::where('id', $sourceId)
                    ->where('user_id', $userId)
                    ->first();
            case 'resource':
                return Resource::where('id', $sourceId)
                    ->where('user_id', $userId)
                    ->first();
            default:
                return null;
        }
    }

    private function extractConfiguration($source, array $templateData): array
    {
        // 简化的配置提取逻辑
        $configuration = [
            'default_settings' => [],
            'customizable_fields' => [],
            'required_fields' => []
        ];

        if ($source instanceof Project) {
            $configuration['default_settings'] = $source->settings ?? [];
            $configuration['customizable_fields'] = ['name', 'description', 'type'];
            $configuration['required_fields'] = ['name'];
        } elseif ($source instanceof Resource) {
            $configuration['default_settings'] = $source->generation_config ?? [];
            $configuration['customizable_fields'] = ['resource_type', 'generation_config'];
            $configuration['required_fields'] = ['resource_type'];
        }

        return array_merge($configuration, $templateData['configuration'] ?? []);
    }

    private function createFromTemplate(Template $template, int $userId, array $useData)
    {
        switch ($template->type) {
            case 'project':
                return $this->createProjectFromTemplate($template, $userId, $useData);
            case 'resource':
                return $this->createResourceFromTemplate($template, $userId, $useData);
            default:
                return null;
        }
    }

    private function createProjectFromTemplate(Template $template, int $userId, array $useData)
    {
        $configuration = $template->configuration;
        $settings = $useData['apply_settings'] ? ($configuration['default_settings'] ?? []) : [];
        
        // 应用自定义配置
        foreach ($useData['customization'] as $key => $value) {
            if (in_array($key, $configuration['customizable_fields'] ?? [])) {
                $settings[$key] = $value;
            }
        }

        return Project::create([
            'user_id' => $userId,
            'name' => $useData['name'],
            'description' => $settings['description'] ?? '',
            'type' => $settings['type'] ?? $template->category,
            'is_public' => false,
            'status' => Project::STATUS_ACTIVE,
            'settings' => $settings,
            'metadata' => [
                'created_from_template' => $template->id,
                'template_name' => $template->name
            ]
        ]);
    }

    private function createResourceFromTemplate(Template $template, int $userId, array $useData)
    {
        $configuration = $template->configuration;
        $generationConfig = $useData['apply_settings'] ? ($configuration['default_settings'] ?? []) : [];
        
        // 应用自定义配置
        foreach ($useData['customization'] as $key => $value) {
            if (in_array($key, $configuration['customizable_fields'] ?? [])) {
                $generationConfig[$key] = $value;
            }
        }

        return Resource::create([
            'user_id' => $userId,
            'resource_type' => $template->category,
            'generation_config' => $generationConfig,
            'status' => Resource::STATUS_PROCESSING,
            'metadata' => [
                'created_from_template' => $template->id,
                'template_name' => $template->name,
                'template_data' => $useData
            ]
        ]);
    }

    private function getRedirectUrl(string $type, int $id): string
    {
        switch ($type) {
            case 'project':
                return "/projects/{$id}";
            case 'resource':
                return "/resources/{$id}";
            default:
                return "/";
        }
    }

    private function getFeaturedTemplates(): array
    {
        $featured = Template::where('featured', true)
            ->where('visibility', Template::VISIBILITY_PUBLIC)
            ->orderBy('usage_count', 'desc')
            ->limit(5)
            ->get();

        $featuredList = [];
        foreach ($featured as $template) {
            $featuredList[] = [
                'template_id' => $template->id,
                'name' => $template->name,
                'category' => $template->category,
                'usage_count' => $template->usage_count,
                'rating' => $template->rating
            ];
        }

        return $featuredList;
    }

    private function getCategoryStats(): array
    {
        $categories = Template::where('visibility', Template::VISIBILITY_PUBLIC)
            ->selectRaw('category, COUNT(*) as count')
            ->groupBy('category')
            ->get();

        $categoryList = [];
        foreach ($categories as $category) {
            $categoryList[] = [
                'name' => $category->category,
                'count' => $category->count
            ];
        }

        return $categoryList;
    }

    private function getTemplateReviews(int $templateId): array
    {
        // 简化的评论实现
        return [
            [
                'user_id' => 789,
                'username' => '用户1',
                'rating' => 5,
                'comment' => '非常好用的模板',
                'created_at' => '2024-01-01 14:00:00'
            ]
        ];
    }

    private function calculateUserTemplateStatistics(int $userId): array
    {
        $templates = Template::where('user_id', $userId)->get();

        return [
            'total_templates' => $templates->count(),
            'public_templates' => $templates->where('visibility', Template::VISIBILITY_PUBLIC)->count(),
            'private_templates' => $templates->where('visibility', Template::VISIBILITY_PRIVATE)->count(),
            'total_usage' => $templates->sum('usage_count'),
            'average_rating' => $templates->avg('rating') ?? 0,
            'total_reviews' => $templates->sum('review_count')
        ];
    }
}
