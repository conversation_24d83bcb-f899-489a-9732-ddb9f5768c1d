<?php

namespace App\Services;

class Service
{
    /**
     * 清理和脱敏请求数据以用于日志记录
     * @param array $data
     * @return array
     */
    private function sanitize_request_for_log(array $data): array
    {
        $sensitiveKeys = [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'credit_card_number',
            'cvv'
        ];

        foreach ($sensitiveKeys as $key) {
            if (array_key_exists($key, $data)) {
                // 将敏感字段的值替换为掩码
                $data[$key] = '********';
            }
        }

        // 处理可能的超大字段或文件上传
        foreach ($data as $key => &$value) {
            if (is_string($value) && mb_strlen($value) > 1024) {
                // 截断过长的字符串
                $value = mb_substr($value, 0, 1024) . '... [TRUNCATED]';
            }
        }

        return $data;
    }
}
