<?php
/**
 * CogniAud 控制器try-catch架构全面检测脚本
 * 检测所有控制器的try-catch实现情况
 */

class TryCatchArchitectureChecker
{
    private $controllersPath;
    private $results = [];
    private $summary = [
        'total_controllers' => 0,
        'total_methods' => 0,
        'methods_with_try_catch' => 0,
        'methods_without_try_catch' => 0,
        'controllers_with_log_import' => 0,
        'controllers_without_log_import' => 0,
        'valid_catch_blocks' => 0,
        'invalid_catch_blocks' => 0
    ];

    public function __construct($controllersPath)
    {
        $this->controllersPath = $controllersPath;
    }

    /**
     * 执行全面检测
     */
    public function runFullCheck()
    {
        echo "🔍 CogniAud 控制器try-catch架构全面检测开始...\n";
        echo "检测路径: {$this->controllersPath}\n";
        echo str_repeat("=", 80) . "\n";

        $controllers = $this->getControllerFiles();
        $this->summary['total_controllers'] = count($controllers);

        foreach ($controllers as $controller) {
            $this->checkController($controller);
        }

        $this->generateReport();
    }

    /**
     * 获取所有控制器文件
     */
    private function getControllerFiles()
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->controllersPath)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }

        sort($files);
        return $files;
    }

    /**
     * 检测单个控制器
     */
    private function checkController($filePath)
    {
        $fileName = basename($filePath);
        echo "\n📁 检测控制器: {$fileName}\n";

        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);

        $result = [
            'file' => $fileName,
            'path' => $filePath,
            'has_log_import' => $this->checkLogImport($content),
            'methods' => [],
            'summary' => [
                'total_methods' => 0,
                'methods_with_try_catch' => 0,
                'methods_without_try_catch' => 0,
                'valid_catch_blocks' => 0,
                'invalid_catch_blocks' => 0
            ]
        ];

        // 检测Log导入
        if ($result['has_log_import']) {
            echo "  ✅ Log导入: 已导入\n";
            $this->summary['controllers_with_log_import']++;
        } else {
            echo "  ❌ Log导入: 未导入\n";
            $this->summary['controllers_without_log_import']++;
        }

        // 查找所有public function方法
        $methods = $this->findPublicMethods($lines);
        $result['summary']['total_methods'] = count($methods);
        $this->summary['total_methods'] += count($methods);

        foreach ($methods as $method) {
            $methodResult = $this->checkMethod($lines, $method);
            $result['methods'][] = $methodResult;

            // 更新统计
            if ($methodResult['has_try_catch']) {
                $result['summary']['methods_with_try_catch']++;
                $this->summary['methods_with_try_catch']++;
            } else {
                $result['summary']['methods_without_try_catch']++;
                $this->summary['methods_without_try_catch']++;
            }

            if ($methodResult['valid_catch_block']) {
                $result['summary']['valid_catch_blocks']++;
                $this->summary['valid_catch_blocks']++;
            } else {
                $result['summary']['invalid_catch_blocks']++;
                $this->summary['invalid_catch_blocks']++;
            }

            // 输出方法检测结果
            $status = $methodResult['has_try_catch'] ? '✅' : '❌';
            $catchStatus = $methodResult['valid_catch_block'] ? '✅' : '❌';
            echo "    {$status} {$methodResult['name']} - try-catch: {$status} | catch块: {$catchStatus}\n";
        }

        echo "  📊 方法统计: 总计{$result['summary']['total_methods']}个, ";
        echo "已改造{$result['summary']['methods_with_try_catch']}个, ";
        echo "未改造{$result['summary']['methods_without_try_catch']}个\n";

        $this->results[] = $result;
    }

    /**
     * 检测Log导入
     */
    private function checkLogImport($content)
    {
        return preg_match('/use\s+Illuminate\\\\Support\\\\Facades\\\\Log;/', $content);
    }

    /**
     * 查找所有public function方法
     */
    private function findPublicMethods($lines)
    {
        $methods = [];
        
        for ($i = 0; $i < count($lines); $i++) {
            $line = trim($lines[$i]);
            
            // 匹配public function，排除构造函数
            if (preg_match('/public\s+function\s+(\w+)\s*\(/', $line, $matches)) {
                $methodName = $matches[1];
                
                // 排除构造函数
                if ($methodName !== '__construct') {
                    $methods[] = [
                        'name' => $methodName,
                        'line_number' => $i + 1,
                        'line_index' => $i
                    ];
                }
            }
        }
        
        return $methods;
    }

    /**
     * 检测方法的try-catch架构
     */
    private function checkMethod($lines, $method)
    {
        $methodName = $method['name'];
        $startLine = $method['line_index'];
        
        $result = [
            'name' => $methodName,
            'line_number' => $method['line_number'],
            'has_try_catch' => false,
            'try_line' => null,
            'catch_line' => null,
            'valid_catch_block' => false,
            'catch_issues' => []
        ];

        // 查找方法体的开始和结束
        $methodStart = $this->findMethodStart($lines, $startLine);
        $methodEnd = $this->findMethodEnd($lines, $methodStart);

        if ($methodStart === null || $methodEnd === null) {
            return $result;
        }

        // 检查方法体第一行是否为try {
        $firstCodeLine = $this->findFirstCodeLine($lines, $methodStart, $methodEnd);
        if ($firstCodeLine !== null) {
            $line = trim($lines[$firstCodeLine]);
            if ($line === 'try {') {
                $result['has_try_catch'] = true;
                $result['try_line'] = $firstCodeLine + 1;
                
                // 查找对应的catch块
                $catchLine = $this->findCatchBlock($lines, $firstCodeLine, $methodEnd);
                if ($catchLine !== null) {
                    $result['catch_line'] = $catchLine + 1;
                    $result['valid_catch_block'] = $this->validateCatchBlock($lines, $catchLine, $methodEnd);
                    
                    if (!$result['valid_catch_block']) {
                        $result['catch_issues'] = $this->getCatchBlockIssues($lines, $catchLine, $methodEnd);
                    }
                }
            }
        }

        return $result;
    }

    /**
     * 查找方法体开始位置（第一个{后）
     */
    private function findMethodStart($lines, $startLine)
    {
        for ($i = $startLine; $i < count($lines); $i++) {
            if (strpos($lines[$i], '{') !== false) {
                return $i;
            }
        }
        return null;
    }

    /**
     * 查找方法体结束位置
     */
    private function findMethodEnd($lines, $methodStart)
    {
        $braceCount = 0;
        $started = false;

        for ($i = $methodStart; $i < count($lines); $i++) {
            $line = $lines[$i];

            // 计算大括号
            $openBraces = substr_count($line, '{');
            $closeBraces = substr_count($line, '}');

            if (!$started && $openBraces > 0) {
                $started = true;
                $braceCount = $openBraces - $closeBraces;
            } else if ($started) {
                $braceCount += $openBraces - $closeBraces;
            }

            if ($started && $braceCount <= 0) {
                return $i;
            }
        }

        return null;
    }

    /**
     * 查找方法体中第一行有效代码
     */
    private function findFirstCodeLine($lines, $methodStart, $methodEnd)
    {
        for ($i = $methodStart + 1; $i <= $methodEnd; $i++) {
            $line = trim($lines[$i]);

            // 跳过空行和注释
            if (empty($line) || strpos($line, '//') === 0 || strpos($line, '/*') === 0 || strpos($line, '*') === 0) {
                continue;
            }

            return $i;
        }

        return null;
    }

    /**
     * 查找catch块
     */
    private function findCatchBlock($lines, $tryLine, $methodEnd)
    {
        for ($i = $tryLine + 1; $i <= $methodEnd; $i++) {
            $line = trim($lines[$i]);
            if (preg_match('/}\s*catch\s*\(/', $line)) {
                return $i;
            }
        }
        return null;
    }

    /**
     * 验证catch块是否符合标准
     */
    private function validateCatchBlock($lines, $catchLine, $methodEnd)
    {
        $catchContent = $this->getCatchBlockContent($lines, $catchLine, $methodEnd);

        // 检查必需的元素 - 使用更宽松的匹配
        $hasLogError = preg_match('/Log::error\s*\(/i', $catchContent);
        $hasMethod = preg_match('/__METHOD__/', $catchContent);
        $hasUserId = preg_match('/[\'"]user_id[\'"].*?(?:\$user->id|\$user\[\'id\'\]|auth\(\)->id)/i', $catchContent);
        $hasRequestData = preg_match('/[\'"]request_data[\'"].*?\$request->all\(\)/i', $catchContent);
        $hasErrorMessage = preg_match('/[\'"]error[\'"].*?\$e->getMessage\(\)/i', $catchContent);
        $hasTrace = preg_match('/[\'"]trace[\'"].*?\$e->getTraceAsString\(\)/i', $catchContent);
        $hasErrorResponse = preg_match('/errorResponse\s*\(.*?ApiCodeEnum::CONTROLLER_ERROR.*?\[\]/i', $catchContent);

        return $hasLogError && $hasMethod && $hasUserId && $hasRequestData &&
               $hasErrorMessage && $hasTrace && $hasErrorResponse;
    }

    /**
     * 获取catch块内容
     */
    private function getCatchBlockContent($lines, $catchLine, $methodEnd)
    {
        $content = '';
        $braceCount = 0;
        $started = false;

        for ($i = $catchLine; $i <= $methodEnd; $i++) {
            $line = $lines[$i];
            $content .= $line . "\n";

            $openBraces = substr_count($line, '{');
            $closeBraces = substr_count($line, '}');

            if (!$started && $openBraces > 0) {
                $started = true;
                $braceCount = $openBraces - $closeBraces;
            } else if ($started) {
                $braceCount += $openBraces - $closeBraces;
            }

            if ($started && $braceCount <= 0) {
                break;
            }
        }

        return $content;
    }

    /**
     * 获取catch块问题列表
     */
    private function getCatchBlockIssues($lines, $catchLine, $methodEnd)
    {
        $issues = [];
        $catchContent = $this->getCatchBlockContent($lines, $catchLine, $methodEnd);

        if (!preg_match('/Log::error\s*\(/i', $catchContent)) {
            $issues[] = '缺少Log::error调用';
        }

        if (!preg_match('/__METHOD__/', $catchContent)) {
            $issues[] = '缺少__METHOD__';
        }

        if (!preg_match('/[\'"]user_id[\'"].*?(?:\$user->id|\$user\[\'id\'\]|auth\(\)->id)/i', $catchContent)) {
            $issues[] = '缺少user_id记录';
        }

        if (!preg_match('/[\'"]request_data[\'"].*?\$request->all\(\)/i', $catchContent)) {
            $issues[] = '缺少request_data记录';
        }

        if (!preg_match('/[\'"]error[\'"].*?\$e->getMessage\(\)/i', $catchContent)) {
            $issues[] = '缺少error message记录';
        }

        if (!preg_match('/[\'"]trace[\'"].*?\$e->getTraceAsString\(\)/i', $catchContent)) {
            $issues[] = '缺少trace记录';
        }

        if (!preg_match('/errorResponse\s*\(.*?ApiCodeEnum::CONTROLLER_ERROR.*?\[\]/i', $catchContent)) {
            $issues[] = '缺少标准errorResponse格式';
        }

        return $issues;
    }

    /**
     * 生成检测报告
     */
    private function generateReport()
    {
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "🎯 CogniAud 控制器try-catch架构检测报告\n";
        echo str_repeat("=", 80) . "\n";

        // 总体统计
        echo "\n📊 总体统计:\n";
        echo "  总控制器数: {$this->summary['total_controllers']}\n";
        echo "  总方法数: {$this->summary['total_methods']}\n";
        echo "  已改造方法数: {$this->summary['methods_with_try_catch']}\n";
        echo "  未改造方法数: {$this->summary['methods_without_try_catch']}\n";
        echo "  Log导入完成: {$this->summary['controllers_with_log_import']}\n";
        echo "  Log导入缺失: {$this->summary['controllers_without_log_import']}\n";
        echo "  有效catch块: {$this->summary['valid_catch_blocks']}\n";
        echo "  无效catch块: {$this->summary['invalid_catch_blocks']}\n";

        // 计算完成率
        $methodCompletionRate = $this->summary['total_methods'] > 0 ?
            round(($this->summary['methods_with_try_catch'] / $this->summary['total_methods']) * 100, 2) : 0;
        $controllerCompletionRate = $this->summary['total_controllers'] > 0 ?
            round(($this->summary['controllers_with_log_import'] / $this->summary['total_controllers']) * 100, 2) : 0;

        echo "\n📈 完成率:\n";
        echo "  方法try-catch改造完成率: {$methodCompletionRate}%\n";
        echo "  控制器Log导入完成率: {$controllerCompletionRate}%\n";

        // 详细问题报告
        echo "\n❌ 问题详情:\n";

        $hasIssues = false;

        foreach ($this->results as $result) {
            $controllerIssues = [];

            // 检查Log导入
            if (!$result['has_log_import']) {
                $controllerIssues[] = "缺少Log导入";
            }

            // 检查方法问题
            $methodIssues = [];
            foreach ($result['methods'] as $method) {
                if (!$method['has_try_catch']) {
                    $methodIssues[] = "{$method['name']}(行{$method['line_number']}): 缺少try-catch";
                } elseif (!$method['valid_catch_block']) {
                    $issues = implode(', ', $method['catch_issues']);
                    $methodIssues[] = "{$method['name']}(行{$method['line_number']}): catch块问题 - {$issues}";
                }
            }

            if (!empty($controllerIssues) || !empty($methodIssues)) {
                $hasIssues = true;
                echo "\n  📁 {$result['file']}:\n";

                foreach ($controllerIssues as $issue) {
                    echo "    ❌ {$issue}\n";
                }

                foreach ($methodIssues as $issue) {
                    echo "    ❌ {$issue}\n";
                }
            }
        }

        if (!$hasIssues) {
            echo "  ✅ 所有控制器都已完成try-catch架构改造！\n";
        }

        // 生成JSON报告
        $this->generateJsonReport();

        echo "\n" . str_repeat("=", 80) . "\n";
        echo "🎯 检测完成！详细报告已保存到 try_catch_report.json\n";
        echo str_repeat("=", 80) . "\n";
    }

    /**
     * 生成JSON格式的详细报告
     */
    private function generateJsonReport()
    {
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'summary' => $this->summary,
            'completion_rates' => [
                'method_try_catch' => $this->summary['total_methods'] > 0 ?
                    round(($this->summary['methods_with_try_catch'] / $this->summary['total_methods']) * 100, 2) : 0,
                'controller_log_import' => $this->summary['total_controllers'] > 0 ?
                    round(($this->summary['controllers_with_log_import'] / $this->summary['total_controllers']) * 100, 2) : 0
            ],
            'controllers' => $this->results
        ];

        file_put_contents('try_catch_report.json', json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}

// 执行检测
$checker = new TryCatchArchitectureChecker('D:\longtool\phpStudy_64\WWW\tool_api\php\api\app\Http\Controllers\Api');
$checker->runFullCheck();
