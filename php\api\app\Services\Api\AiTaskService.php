<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * AI任务服务类 - 统一版
 * 负责处理AI任务的创建、执行、监控和管理
 * 整合了 TaskManagementService 的功能
 */
class AiTaskService extends Service
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }
    /**
     * 创建AI任务
     */
    public function createTask($type, $params = [], $priority = 'normal')
    {
        try {
            $taskId = 'task_' . Str::uuid();
            
            $task = [
                'id' => $taskId,
                'type' => $type,
                'params' => $params,
                'priority' => $priority,
                'status' => 'pending',
                'progress' => 0,
                'created_at' => now(),
                'updated_at' => now(),
                'estimated_duration' => $this->estimateTaskDuration($type),
                'retry_count' => 0,
                'max_retries' => 3
            ];
            
            // 缓存任务信息
            Cache::put("ai_task_{$taskId}", $task, 3600);
            
            // 添加到任务队列
            $this->addToQueue($task);
            
            Log::info("AI任务创建成功", ['task_id' => $taskId, 'type' => $type]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'AI任务创建成功',
                'data' => [
                    'task_id' => $taskId,
                    'status' => 'pending',
                    'estimated_duration' => $task['estimated_duration']
                ]
            ];
        } catch (\Exception $e) {
            Log::error("AI任务创建失败", ['error' => $e->getMessage()]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'AI任务创建失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取任务状态
     */
    public function getTaskStatus($taskId)
    {
        try {
            $task = Cache::get("ai_task_{$taskId}");
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => null
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务状态获取成功',
                'data' => [
                    'task_id' => $taskId,
                    'status' => $task['status'],
                    'progress' => $task['progress'],
                    'created_at' => $task['created_at'],
                    'updated_at' => $task['updated_at'],
                    'result' => $task['result'] ?? null,
                    'error' => $task['error'] ?? null
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务状态获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 取消任务 - 整合自 TaskManagementService
     */
    public function cancelTask(int $taskId, int $userId, string $reason = '用户主动取消'): array
    {
        try {
            DB::beginTransaction();

            // 首先查找任务（不限制用户）
            $task = AiGenerationTask::where('id', $taskId)->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            // 检查权限：用户只能取消自己的任务
            if ($task->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '用户无权取消该任务',
                    'data' => []
                ];
            }

            // 检查任务状态是否允许取消
            if (!in_array($task->status, ['pending', 'processing'])) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '任务状态不允许取消',
                    'data' => []
                ];
            }

            // 计算退款积分
            $refundAmount = 0;
            if ($task->status === 'pending') {
                // 如果任务还未开始，全额退款
                $refundAmount = $task->cost_points;
            } elseif ($task->status === 'processing') {
                // 如果任务正在处理，部分退款（50%）
                $refundAmount = $task->cost_points * 0.5;
            }

            // 更新任务状态
            $task->status = 'cancelled';
            $task->cancelled_at = Carbon::now();
            $task->cancel_reason = $reason;
            $task->updated_at = Carbon::now();
            $task->save();

            // 如果有退款，处理积分返还
            if ($refundAmount > 0) {
                $pointsResult = $this->pointsService->refundPoints(
                    $userId,
                    $refundAmount,
                    'task_cancel',
                    $taskId
                );

                if ($pointsResult['code'] !== ApiCodeEnum::SUCCESS) {
                    DB::rollBack();
                    return [
                        'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                        'message' => '积分退款失败',
                        'data' => []
                    ];
                }
            }

            DB::commit();

            Log::info("任务取消成功", [
                'task_id' => $taskId,
                'user_id' => $userId,
                'reason' => $reason,
                'refund_amount' => $refundAmount
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务已成功取消',
                'data' => [
                    'task_id' => $taskId,
                    'status' => 'cancelled',
                    'refund_amount' => number_format($refundAmount, 4)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("任务取消失败", [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务取消失败',
                'data' => []
            ];
        }
    }
    
    /**
     * 获取任务列表
     */
    public function getTaskList($filters = [], $page = 1, $limit = 20)
    {
        try {
            // 模拟任务列表数据
            $tasks = [
                [
                    'id' => 'task_001',
                    'type' => 'story_generation',
                    'status' => 'completed',
                    'progress' => 100,
                    'created_at' => now()->subHours(2),
                    'duration' => 45
                ],
                [
                    'id' => 'task_002',
                    'type' => 'image_generation',
                    'status' => 'running',
                    'progress' => 65,
                    'created_at' => now()->subMinutes(30),
                    'duration' => null
                ],
                [
                    'id' => 'task_003',
                    'type' => 'voice_synthesis',
                    'status' => 'pending',
                    'progress' => 0,
                    'created_at' => now()->subMinutes(5),
                    'duration' => null
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务列表获取成功',
                'data' => [
                    'tasks' => $tasks,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => count($tasks),
                        'total_pages' => 1
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务列表获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 重试任务
     */
    public function retryTask($taskId, $userId, $options = [])
    {
        try {
            $task = Cache::get("ai_task_{$taskId}");
            
            if (!$task) {
                return response()->json([
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => null
                ], 404);
            }
            
            // 验证任务所有权
            if ($task['user_id'] != $userId) {
                return response()->json([
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权限操作此任务',
                    'data' => null
                ], 403);
            }
            
            if ($task['retry_count'] >= $task['max_retries']) {
                return response()->json([
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '任务重试次数已达上限',
                    'data' => null
                ], 400);
            }
            
            // 创建新任务ID
            $newTaskId = 'task_' . Str::uuid();
            
            $newTask = $task;
            $newTask['id'] = $newTaskId;
            $newTask['status'] = 'pending';
            $newTask['progress'] = 0;
            $newTask['retry_count'] = $task['retry_count'] + 1;
            $newTask['updated_at'] = now();
            $newTask['error'] = null;
            
            // 如果选择使用不同平台
            if ($options['use_different_platform'] ?? false) {
                $newTask['ai_platform'] = $this->getAlternativePlatform($task['ai_platform']);
            }
            
            Cache::put("ai_task_{$newTaskId}", $newTask, 3600);
            
            // 重新添加到队列
            $this->addToQueue($newTask);
            
            Log::info("AI任务重试成功", [
                'original_task_id' => $taskId,
                'new_task_id' => $newTaskId,
                'user_id' => $userId,
                'retry_count' => $newTask['retry_count']
            ]);
            
            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务重试成功',
                'data' => [
                    'task_id' => $taskId,
                    'new_task_id' => $newTaskId,
                    'status' => 'pending',
                    'estimated_time' => $newTask['estimated_duration'],
                    'retry_count' => $newTask['retry_count']
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("AI任务重试失败", ['task_id' => $taskId, 'user_id' => $userId, 'error' => $e->getMessage()]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务重试失败',
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量查询任务状态 - 整合自 TaskManagementService
     */
    public function getBatchTaskStatus(array $taskIds, int $userId): array
    {
        try {
            if (empty($taskIds)) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '任务ID列表不能为空',
                    'data' => []
                ];
            }

            // 限制批量查询的数量
            if (count($taskIds) > 100) {
                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '批量查询任务数量不能超过100个',
                    'data' => []
                ];
            }

            // 查询任务，只返回用户自己的任务
            $tasks = AiGenerationTask::whereIn('id', $taskIds)
                ->where('user_id', $userId)
                ->select(['id', 'task_type', 'status', 'progress', 'created_at', 'updated_at', 'error_message'])
                ->get();

            $taskData = [];
            $summary = [
                'total' => count($taskIds),
                'found' => $tasks->count(),
                'pending' => 0,
                'processing' => 0,
                'completed' => 0,
                'failed' => 0,
                'cancelled' => 0
            ];

            foreach ($tasks as $task) {
                $taskData[] = [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'progress' => $task->progress ?? 0,
                    'created_at' => $task->created_at->toISOString(),
                    'updated_at' => $task->updated_at->toISOString(),
                    'error_message' => $task->error_message
                ];

                // 统计各状态数量
                if (isset($summary[$task->status])) {
                    $summary[$task->status]++;
                }
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '批量查询成功',
                'data' => [
                    'tasks' => $taskData,
                    'summary' => $summary
                ]
            ];

        } catch (\Exception $e) {
            Log::error("批量查询任务状态失败", [
                'task_ids' => $taskIds,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量查询失败',
                'data' => []
            ];
        }
    }

    /**
     * 查询任务恢复状态 - 整合自 TaskManagementService
     */
    public function getTaskRecoveryStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::where('id', $taskId)->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($task->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '用户无权查看该任务',
                    'data' => []
                ];
            }

            // 分析任务恢复状态
            $canRecover = false;
            $recoveryOptions = [];
            $recoveryStatus = 'not_recoverable';

            if ($task->status === 'failed') {
                $canRecover = true;
                $recoveryStatus = 'recoverable';
                $recoveryOptions = [
                    'retry_same_platform' => '使用相同平台重试',
                    'retry_different_platform' => '切换平台重试',
                    'manual_review' => '提交人工审核'
                ];
            } elseif ($task->status === 'cancelled') {
                $canRecover = true;
                $recoveryStatus = 'recoverable';
                $recoveryOptions = [
                    'restart_task' => '重新启动任务'
                ];
            } elseif ($task->status === 'processing') {
                // 检查是否长时间无响应
                $lastUpdate = Carbon::parse($task->updated_at);
                $minutesSinceUpdate = $lastUpdate->diffInMinutes(Carbon::now());

                if ($minutesSinceUpdate > 30) { // 30分钟无更新认为可能需要恢复
                    $canRecover = true;
                    $recoveryStatus = 'may_need_recovery';
                    $recoveryOptions = [
                        'check_status' => '检查任务状态',
                        'force_restart' => '强制重启任务'
                    ];
                }
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '查询成功',
                'data' => [
                    'task_id' => $taskId,
                    'current_status' => $task->status,
                    'recovery_status' => $recoveryStatus,
                    'can_recover' => $canRecover,
                    'recovery_options' => $recoveryOptions,
                    'last_updated' => $task->updated_at->toISOString(),
                    'retry_count' => $task->retry_count
                ]
            ];

        } catch (\Exception $e) {
            Log::error("查询任务恢复状态失败", [
                'task_id' => $taskId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '查询失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取任务超时配置 - 整合自 TaskManagementService
     */
    public function getTimeoutConfig(): array
    {
        try {
            $config = [
                'default_timeout' => 300,  // 5分钟
                'max_timeout' => 1800,     // 30分钟
                'retry_timeout' => 60,     // 1分钟
                'batch_timeout' => 3600,   // 1小时
                'cleanup_timeout' => 86400 // 24小时
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取超时配置成功',
                'data' => [
                    'timeout_config' => $config
                ]
            ];

        } catch (\Exception $e) {
            Log::error("获取超时配置失败", [
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取超时配置失败',
                'data' => []
            ];
        }
    }

    /**
     * 估算任务执行时间
     */
    private function estimateTaskDuration($type)
    {
        $durations = [
            'story_generation' => 30,
            'image_generation' => 60,
            'voice_synthesis' => 45,
            'video_generation' => 120,
            'text_analysis' => 15
        ];
        
        return $durations[$type] ?? 60;
    }
    
    /**
     * 添加任务到队列
     */
    private function addToQueue($task)
    {
        // 模拟添加到队列
        $queueKey = "ai_task_queue_{$task['priority']}";
        $queue = Cache::get($queueKey, []);
        $queue[] = $task['id'];
        Cache::put($queueKey, $queue, 3600);
    }
    
    /**
     * 获取用户任务列表
     */
    public function getUserTasks($userId, $filters = [])
    {
        try {
            // 模拟用户任务数据
            $tasks = [
                [
                    'id' => 1,
                    'type' => 'image',
                    'status' => 'completed',
                    'prompt' => '一只可爱的小猫',
                    'result_url' => 'https://api.tiptop.cn/files/123.jpg',
                    'progress' => 100,
                    'created_at' => '2024-01-01 12:00:00',
                    'completed_at' => '2024-01-01 12:05:00'
                ],
                [
                    'id' => 2,
                    'type' => 'story',
                    'status' => 'processing',
                    'prompt' => '写一个科幻故事',
                    'result_url' => null,
                    'progress' => 65,
                    'created_at' => '2024-01-01 13:00:00',
                    'completed_at' => null
                ]
            ];
            
            // 应用过滤器
            if (!empty($filters['type'])) {
                $tasks = array_filter($tasks, function($task) use ($filters) {
                    return $task['type'] === $filters['type'];
                });
            }
            
            if (!empty($filters['status'])) {
                $tasks = array_filter($tasks, function($task) use ($filters) {
                    return $task['status'] === $filters['status'];
                });
            }
            
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 20;
            $total = count($tasks);
            $lastPage = ceil($total / $perPage);
            
            // 分页
            $offset = ($page - 1) * $perPage;
            $tasks = array_slice($tasks, $offset, $perPage);
            
            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'tasks' => array_values($tasks),
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("获取用户任务列表失败", ['user_id' => $userId, 'error' => $e->getMessage()]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务列表失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取任务详情
     */
    public function getTaskDetail($taskId, $userId)
    {
        try {
            // 模拟任务详情数据
            $task = [
                'id' => $taskId,
                'type' => 'image',
                'status' => 'completed',
                'prompt' => '一只可爱的小猫',
                'parameters' => [
                    'style' => 'cartoon',
                    'size' => '1024x1024',
                    'quality' => 'high'
                ],
                'result_url' => 'https://api.tiptop.cn/files/123.jpg',
                'progress' => 100,
                'error_message' => null,
                'ai_platform' => 'liblib',
                'cost_points' => 10,
                'created_at' => '2024-01-01 12:00:00',
                'started_at' => '2024-01-01 12:01:00',
                'completed_at' => '2024-01-01 12:05:00'
            ];
            
            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $task
            ]);
        } catch (\Exception $e) {
            Log::error("获取任务详情失败", ['task_id' => $taskId, 'user_id' => $userId, 'error' => $e->getMessage()]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务详情失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取任务统计
     */
    public function getTaskStats($userId, $period = 'all')
    {
        try {
            // 模拟统计数据
            $stats = [
                'total_tasks' => 150,
                'completed_tasks' => 120,
                'failed_tasks' => 20,
                'pending_tasks' => 10,
                'total_points_used' => 1500,
                'success_rate' => 80.0,
                'by_type' => [
                    'image' => 80,
                    'story' => 30,
                    'video' => 20,
                    'voice' => 15,
                    'music' => 3,
                    'sound' => 2
                ]
            ];
            
            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error("获取任务统计失败", ['user_id' => $userId, 'period' => $period, 'error' => $e->getMessage()]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务统计失败',
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 计算退款积分
     */
    private function calculateRefundPoints($task)
    {
        $costPoints = $task['cost_points'] ?? 10;
        $progress = $task['progress'] ?? 0;
        
        // 根据进度计算退款比例
        if ($progress < 10) {
            return $costPoints; // 全额退款
        } elseif ($progress < 50) {
            return intval($costPoints * 0.7); // 70%退款
        } elseif ($progress < 80) {
            return intval($costPoints * 0.3); // 30%退款
        } else {
            return 0; // 不退款
        }
    }
    
    /**
     * 获取替代AI平台
     */
    private function getAlternativePlatform($currentPlatform)
    {
        $platforms = ['liblib', 'midjourney', 'stable_diffusion', 'dalle'];
        $alternatives = array_diff($platforms, [$currentPlatform]);
        
        return $alternatives[array_rand($alternatives)];
    }
    
    /**
     * 获取队列统计
     */
    public function getQueueStats()
    {
        try {
            $stats = [
                'high_priority' => count(Cache::get('ai_task_queue_high', [])),
                'normal_priority' => count(Cache::get('ai_task_queue_normal', [])),
                'low_priority' => count(Cache::get('ai_task_queue_low', [])),
                'total_pending' => 0,
                'total_running' => 2,
                'total_completed' => 156,
                'total_failed' => 3
            ];
            
            $stats['total_pending'] = $stats['high_priority'] + $stats['normal_priority'] + $stats['low_priority'];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '队列统计获取成功',
                'data' => $stats
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '队列统计获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}