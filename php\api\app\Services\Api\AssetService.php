<?php

namespace App\Services\Api;

use App\Services\Service;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * 素材管理服务
 * 
 * 按照新规范要求创建，修复500错误
 * 参考dev-api-guidelines-add.mdc规范
 */
class AssetService extends Service
{
    /**
     * 获取素材列表
     * 
     * @param string|null $category
     * @param string|null $type
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getAssetList($category = null, $type = null, $page = 1, $limit = 20)
    {
        try {
            $query = DB::table('user_assets')
                ->select([
                    'id',
                    'asset_name',
                    'file_type',
                    'category',
                    'file_size',
                    'file_url',
                    'created_at'
                ])
                ->where('deleted_at', null);
            
            // 添加筛选条件
            if ($category) {
                $query->where('category', $category);
            }
            
            if ($type) {
                $query->where('file_type', $type);
            }
            
            // 分页
            $offset = ($page - 1) * $limit;
            $total = $query->count();
            $assets = $query->orderBy('created_at', 'desc')
                          ->offset($offset)
                          ->limit($limit)
                          ->get();
            
            return [
                'assets' => $assets,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ];
            
        } catch (\Exception $e) {
            // 如果表不存在，返回空结果
            return [
                'assets' => [],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => $limit,
                    'total' => 0,
                    'total_pages' => 0
                ]
            ];
        }
    }
    
    /**
     * 上传素材
     * 修复422错误 - 支持file和url两种上传方式
     *
     * @param string $fileType
     * @param string $category
     * @param \Illuminate\Http\UploadedFile|null $file
     * @param string|null $url
     * @param string $title
     * @param string $description
     * @return array
     */
    public function uploadAsset($fileType, $category, $file = null, $url = null, $title = '', $description = '')
    {
        try {
            $fileName = '';
            $filePath = '';
            $fileSize = 0;

            if ($file) {
                // 文件上传方式
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = 'assets/' . $category . '/' . $fileName;
                $fileSize = $file->getSize();
            } elseif ($url) {
                // URL方式
                $fileName = time() . '_' . basename(parse_url($url, PHP_URL_PATH));
                $filePath = $url; // 直接使用URL
                $fileSize = 0; // URL方式无法获取大小
            } else {
                throw new \Exception('必须提供文件或URL');
            }
            
            // 处理文件URL和其他信息
            if ($file) {
                // 文件上传方式
                $fileUrl = '/storage/' . $filePath;
                $originalName = $file->getClientOriginalName();
                $mimeType = $file->getMimeType();
                $fileSize = $file->getSize();
            } else {
                // URL方式
                $fileUrl = $filePath; // 直接使用URL
                $originalName = $fileName;
                $mimeType = $this->getMimeTypeFromUrl($url);
                $fileSize = 0;
            }

            // 保存到数据库
            $assetId = DB::table('user_assets')->insertGetId([
                'user_id' => 1, // 临时用户ID
                'asset_name' => $title ?: $originalName,
                'file_type' => $fileType,
                'category' => $category,
                'file_url' => $fileUrl,
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'mime_type' => $mimeType,
                'description' => $description,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
            
            return [
                'asset_id' => $assetId,
                'asset_name' => $title ?: $originalName,
                'file_type' => $fileType,
                'category' => $category,
                'file_url' => $fileUrl,
                'file_size' => $fileSize,
                'uploaded_at' => Carbon::now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            throw new \Exception('素材上传失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取素材详情
     * 
     * @param int $id
     * @return array|null
     */
    public function getAssetDetail($id)
    {
        try {
            $asset = DB::table('user_assets')
                ->where('id', $id)
                ->where('deleted_at', null)
                ->first();
            
            if (!$asset) {
                return null;
            }
            
            return [
                'id' => $asset->id,
                'asset_name' => $asset->asset_name,
                'file_type' => $asset->file_type,
                'category' => $asset->category,
                'file_url' => $asset->file_url,
                'file_size' => $asset->file_size,
                'mime_type' => $asset->mime_type ?? '',
                'created_at' => $asset->created_at,
                'updated_at' => $asset->updated_at
            ];
            
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 删除素材
     * 
     * @param int $id
     * @return bool
     */
    public function deleteAsset($id)
    {
        try {
            $affected = DB::table('user_assets')
                ->where('id', $id)
                ->where('deleted_at', null)
                ->update([
                    'deleted_at' => Carbon::now()
                ]);
            
            return $affected > 0;
            
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 根据URL获取MIME类型
     * 修复422错误 - 添加URL方式上传的MIME类型检测
     */
    private function getMimeTypeFromUrl($url): string
    {
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

        $mimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'mp4' => 'video/mp4',
            'avi' => 'video/avi',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain'
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
