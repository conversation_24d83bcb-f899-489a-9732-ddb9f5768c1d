<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\AiTaskService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * AI任务管理控制器-UI向【工具api接口服务】发送请求
 * 处理AI生成任务的创建、查询、重试等操作
 */
class AiTaskController extends Controller
{
    protected $aiTaskService;

    public function __construct(AiTaskService $aiTaskService)
    {
        $this->aiTaskService = $aiTaskService;
    }

    /**
     * @ApiTitle(获取AI任务列表)
     * @ApiSummary(查询用户的AI任务列表)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="type", type="string", required=false, description="任务类型：image,story,video,voice,music,sound")
     * @ApiParams(name="status", type="string", required=false, description="任务状态：pending,processing,completed,failed")
     * @ApiParams(name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams(name="per_page", type="integer", required=false, description="每页数量，默认20")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "tasks": [
     *       {
     *         "id": 1,
     *         "type": "image",
     *         "status": "completed",
     *         "prompt": "一只可爱的小猫",
     *         "result_url": "https://api.tiptop.cn/files/123.jpg",
     *         "progress": 100,
     *         "created_at": "2024-01-01 12:00:00",
     *         "completed_at": "2024-01-01 12:05:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 50,
     *       "last_page": 3
     *     }
     *   }
     * })
     */
    public function index(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'type' => 'sometimes|string|in:image,story,video,voice,music,sound',
                'status' => 'sometimes|string|in:pending,processing,completed,failed',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'type' => $request->get('type'),
                'status' => $request->get('status'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->aiTaskService->getUserTasks($user->id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取AI任务列表失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取AI任务列表失败', []);
        }
    }

    /**
     * @ApiTitle(获取AI任务详情)
     * @ApiSummary(查询指定AI任务的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 1,
     *     "type": "image",
     *     "status": "completed",
     *     "prompt": "一只可爱的小猫",
     *     "parameters": {
     *       "style": "cartoon",
     *       "size": "1024x1024",
     *       "quality": "high"
     *     },
     *     "result_url": "https://api.tiptop.cn/files/123.jpg",
     *     "progress": 100,
     *     "error_message": null,
     *     "ai_platform": "liblib",
     *     "cost_points": 10,
     *     "created_at": "2024-01-01 12:00:00",
     *     "started_at": "2024-01-01 12:01:00",
     *     "completed_at": "2024-01-01 12:05:00"
     *   }
     * })
     */
    public function show($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->aiTaskService->getTaskDetail($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取AI任务详情失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取AI任务详情失败', []);
        }
    }





    /**
     * @ApiTitle(获取任务统计)
     * @ApiSummary(获取用户的AI任务统计信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/ai/tasks/stats)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="period", type="string", required=false, description="统计周期：today,week,month,all")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "total_tasks": 150,
     *     "completed_tasks": 120,
     *     "failed_tasks": 20,
     *     "pending_tasks": 10,
     *     "total_points_used": 1500,
     *     "success_rate": 80.0,
     *     "by_type": {
     *       "image": 80,
     *       "story": 30,
     *       "video": 20,
     *       "voice": 15,
     *       "music": 3,
     *       "sound": 2
     *     }
     *   }
     * })
     */
    public function stats(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'period' => 'sometimes|string|in:today,week,month,all'
            ];

            $this->validateData($request->all(), $rules);

            $period = $request->get('period', 'all', []);

            $result = $this->aiTaskService->getTaskStats($user->id, $period);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取AI任务统计失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取AI任务统计失败', []);
        }
    }

    /**
     * @ApiTitle(取消任务)
     * @ApiSummary(取消正在进行的AI生成任务)
     * @ApiMethod(POST)
     * @ApiRoute(/api/tasks/{id}/cancel)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiParams(name="reason", type="string", required=false, description="取消原因")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "任务已成功取消",
     *   "data": {
     *     "task_id": 123,
     *     "status": "cancelled",
     *     "refund_amount": "10.0000"
     *   }
     * })
     */
    public function cancel(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'reason' => 'sometimes|string|max:255'
            ];

            $this->validateData($request->all(), $rules);

            $reason = $request->get('reason', '用户主动取消');

            $result = $this->aiTaskService->cancelTask((int)$id, $user->id, $reason);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('取消AI任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '取消AI任务失败', []);
        }
    }

    /**
     * @ApiTitle(重试AI任务)
     * @ApiSummary(重新执行失败的AI任务)
     * @ApiMethod(POST)
     * @ApiRoute(/api/tasks/{id}/retry)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiParams(name="use_different_platform", type="boolean", required=false, description="是否使用不同的AI平台")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "任务重试成功",
     *   "data": {
     *     "task_id": 1,
     *     "new_task_id": 123,
     *     "status": "pending",
     *     "estimated_time": 300,
     *     "retry_count": 2
     *   }
     * })
     */
    public function retry($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'use_different_platform' => 'sometimes|boolean'
            ];

            $this->validateData($request->all(), $rules);

            $options = [
                'use_different_platform' => $request->get('use_different_platform', false)
            ];

            $result = $this->aiTaskService->retryTask($id, $user->id, $options);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('重试AI任务失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '重试AI任务失败', []);
        }
    }


    /**
     * @ApiTitle(批量查询任务状态)
     * @ApiSummary(批量查询多个任务的状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/tasks/batch/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="task_ids", type="string", required=true, description="任务ID列表，逗号分隔")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量查询成功",
     *   "data": {
     *     "tasks": [
     *       {
     *         "id": 123,
     *         "task_type": "image",
     *         "status": "completed",
     *         "progress": 100,
     *         "created_at": "2024-01-01T12:00:00.000000Z",
     *         "updated_at": "2024-01-01T12:05:00.000000Z"
     *       }
     *     ],
     *     "summary": {
     *       "total": 5,
     *       "found": 3,
     *       "pending": 1,
     *       "processing": 0,
     *       "completed": 2,
     *       "failed": 0,
     *       "cancelled": 0
     *     }
     *   }
     * })
     */
    public function batchStatus(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $rules = [
                'task_ids' => 'required|string'
            ];

            $this->validateData($request->all(), $rules);

            $taskIdsString = $request->get('task_ids');
            $taskIds = array_map('intval', explode(',', $taskIdsString));

            $result = $this->aiTaskService->getBatchTaskStatus($taskIds, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量获取任务状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量获取任务状态失败', []);
        }
    }

    /**
     * @ApiTitle(查询任务恢复状态)
     * @ApiSummary(查询指定任务的恢复状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/tasks/{id}/recovery)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="id", type="integer", required=true, description="任务ID")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "查询成功",
     *   "data": {
     *     "task_id": 123,
     *     "current_status": "failed",
     *     "recovery_status": "recoverable",
     *     "can_recover": true,
     *     "recovery_options": {
     *       "retry_same_platform": "使用相同平台重试",
     *       "retry_different_platform": "切换平台重试",
     *       "manual_review": "提交人工审核"
     *     },
     *     "last_updated": "2024-01-01T12:00:00.000000Z",
     *     "retry_count": 1
     *   }
     * })
     */
    public function recovery(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->aiTaskService->getTaskRecoveryStatus((int)$id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取任务恢复状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取任务恢复状态失败', []);
        }
    }

    /**
     * @ApiTitle(获取任务超时配置)
     * @ApiSummary(获取系统任务超时配置信息)
     * @ApiMethod(GET)
     * @ApiRoute(/api/tasks/timeout-config)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "获取超时配置成功",
     *   "data": {
     *     "timeout_config": {
     *       "default_timeout": 300,
     *       "max_timeout": 1800,
     *       "retry_timeout": 60,
     *       "batch_timeout": 3600,
     *       "cleanup_timeout": 86400
     *     }
     *   }
     * })
     */
    public function timeoutConfig(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $result = $this->aiTaskService->getTimeoutConfig();

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取任务超时配置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取任务超时配置失败', []);
        }
    }
}
