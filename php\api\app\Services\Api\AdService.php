<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class AdService extends Service
{
    /**
     * 确保user_ad_logs表存在
     */
    private function ensureTableExists()
    {
        if (!Schema::hasTable('user_ad_logs')) {
            Schema::create('user_ad_logs', function ($table) {
                $table->id();
                $table->unsignedBigInteger('user_id')->comment('用户ID');
                $table->string('uuid', 36)->comment('广告记录UUID');
                $table->unsignedInteger('ad_id')->comment('广告ID');
                $table->unsignedInteger('interval_time')->nullable()->comment('间隔时间（秒）');
                $table->unsignedInteger('time_length')->nullable()->comment('时长（秒）');
                $table->tinyInteger('state')->nullable()->comment('状态。0：进行中，1：加载失败，2：开始播放，3：离开，4：超时，5：结束');
                $table->timestamps();

                $table->index('user_id');
                $table->index('uuid');
                $table->index('created_at');
            });
        }
    }
    /**
     * @param array $data
     * @param string $time
     */
    public function store(array $data, string $time)
    {
        try {
            // 检查表是否存在，如果不存在则创建
            $this->ensureTableExists();

            $interval_time = null;
            try {
                $created_at = DB::table('user_ad_logs')
                    ->where('user_id',$data['user_id'])
                    ->orderByDesc('id')
                    ->value('created_at');
            } catch (\Exception $e) {
                // 如果查询失败，可能是表不存在，再次尝试创建表
                $this->ensureTableExists();
                $created_at = null;
            }
            $now = strtotime($time);
            $today = strtotime('today', $now);
            if(!empty($created_at))
            {
                $created_at = strtotime($created_at);
                if($created_at > $today)
                {
                    $interval_time = $now - $created_at;
                }
            }
            DB::table('user_ad_logs')->insert(array_merge($data,[
                'interval_time' => $interval_time,
                'created_at' => $time,
                'updated_at' => $time
            ]));
            // 简化的广告次数统计，直接使用数据库操作
            // 这里可以根据实际需求添加更复杂的逻辑
            // 暂时跳过用户广告次数统计，只记录广告日志
            Log::info('Ad stored successfully', $data);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '广告记录保存成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            Log::error('广告记录保存失败', [
                'method' => __METHOD__,
                'user_id' => $data['user_id'] ?? null,
                'ad_id' => $data['ad_id'] ?? null,
                'time' => $time,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '广告记录保存失败',
                'data' => []
            ];
        }
    }

    /**
     * @param array $data
     * @param string $time
     */
    public function update(array $data, string $time)
    {
        try {
            // 确保表存在
            $this->ensureTableExists();

            $affected = DB::table('user_ad_logs')
                ->where('user_id', $data['user_id'])
                ->where('uuid', $data['uuid'])
                ->update(array_merge($data, ['updated_at' => $time]));

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '广告记录更新成功',
                'data' => ['affected_rows' => $affected]
            ];

        } catch (\Exception $e) {
            Log::error('广告记录更新失败', [
                'method' => __METHOD__,
                'user_id' => $data['user_id'] ?? null,
                'uuid' => $data['uuid'] ?? null,
                'time' => $time,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '广告记录更新失败',
                'data' => []
            ];
        }
    }
}
