<?php

namespace App\Http\Controllers\Api;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthService;
use App\Services\Api\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * AI图像生成与批量处理
 */
class ImageController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * @ApiTitle(图像生成)
     * @ApiSummary(使用AI生成图像)
     * @ApiMethod(POST)
     * @ApiRoute(/api/images/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="图像生成提示词")
     * @ApiParams(name="character_id", type="int", required=false, description="角色ID")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="style", type="string", required=false, description="图像风格")
     * @ApiParams(name="aspect_ratio", type="string", required=false, description="宽高比：1:1/16:9/9:16")
     * @ApiParams(name="quality", type="string", required=false, description="图像质量：standard/hd")
     * @ApiParams(name="platform", type="string", required=false, description="指定AI平台：liblib/kling/minimax")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.image_url", type="string", required=false, description="生成的图像URL")
     * @ApiReturnParams (name="data.thumbnail_url", type="string", required=false, description="缩略图URL")
     * @ApiReturnParams (name="data.cost", type="decimal", required=false, description="消耗的积分")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "图像生成成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "completed",
     *     "image_url": "https://aiapi.tiptop.cn/images/generated/123.jpg",
     *     "thumbnail_url": "https://aiapi.tiptop.cn/images/thumbnails/123.jpg",
     *     "cost": "0.0200"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        try {
            $rules = [
                'prompt' => 'required|string|min:5|max:2000',
                'character_id' => 'sometimes|integer|exists:character_library,id',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'style' => 'sometimes|string|max:100',
                'aspect_ratio' => 'sometimes|string|in:1:1,16:9,9:16,4:3,3:4',
                'quality' => 'sometimes|string|in:standard,hd',
                'platform' => 'sometimes|string|in:liblib,kling,minimax'
            ];

            $messages = [
                'prompt.required' => '图像提示词不能为空',
                'prompt.min' => '图像提示词至少5个字符',
                'prompt.max' => '图像提示词不能超过2000个字符',
                'character_id.exists' => '角色不存在',
                'project_id.exists' => '项目不存在',
                'aspect_ratio.in' => '宽高比必须是：1:1、16:9、9:16、4:3、3:4之一',
                'quality.in' => '图像质量必须是：standard、hd之一',
                'platform.in' => 'AI平台必须是：liblib、kling、minimax之一'
            ];

            try {
                $this->validateData($request->all(), $rules, $messages, []);
            } catch (\Illuminate\Validation\ValidationException $e) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
            }

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $generationParams = [
                'style' => $request->get('style'),
                'aspect_ratio' => $request->get('aspect_ratio', '16:9'),
                'quality' => $request->get('quality', 'standard'),
                'platform' => $request->get('platform', 'liblib')
            ];

            $result = $this->imageService->generateImage(
                $user->id,
                $request->prompt,
                $request->character_id,
                $request->project_id,
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('图像生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '图像生成失败');
        }
    }

    /**
     * @ApiTitle(图像生成状态查询)
     * @ApiSummary(查询图像生成任务的状态)
     * @ApiMethod(GET)
     * @ApiRoute(/api/images/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "image_generation",
     *     "status": "completed",
     *     "platform": "liblib",
     *     "image_url": "https://aiapi.tiptop.cn/images/generated/123.jpg",
     *     "cost": "0.0200",
     *     "processing_time_ms": 3000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:03"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->imageService->getImageStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取图像生成状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取图像生成状态失败');
        }
    }

    /**
     * @ApiTitle(图像生成结果获取)
     * @ApiSummary(获取图像生成的详细结果)
     * @ApiMethod(GET)
     * @ApiRoute(/api/images/{id}/result)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="结果数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "task_id": 123,
     *     "image_url": "https://aiapi.tiptop.cn/images/generated/123.jpg",
     *     "thumbnail_url": "https://aiapi.tiptop.cn/images/thumbnails/123.jpg",
     *     "metadata": {
     *       "width": 1024,
     *       "height": 1024,
     *       "format": "jpg",
     *       "file_size": "2.5MB"
     *     },
     *     "download_info": {
     *       "direct_url": "https://aiapi.tiptop.cn/images/generated/123.jpg",
     *       "expires_at": "2024-01-02 12:00:00"
     *     }
     *   }
     * })
     */
    public function getResult(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->imageService->getImageResult($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取图像生成结果失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取图像生成结果失败');
        }
    }

    /**
     * @ApiTitle(批量图像生成)
     * @ApiSummary(批量生成多张图像)
     * @ApiMethod(POST)
     * @ApiRoute(/api/batch/images/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompts", type="array", required=true, description="图像提示词数组")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="common_params", type="object", required=false, description="通用参数")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "批量图像生成任务创建成功",
     *   "data": {
     *     "batch_id": "batch_123",
     *     "task_ids": [123, 124, 125],
     *     "total_count": 3,
     *     "estimated_cost": "0.0600"
     *   }
     * })
     */
    public function batchGenerate(Request $request)
    {
        try {
            $rules = [
                'prompts' => 'required|array|min:1|max:10',
                'prompts.*' => 'required|string|min:5|max:2000',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'common_params' => 'sometimes|array'
            ];

            $messages = [
                'prompts.required' => '提示词数组不能为空',
                'prompts.min' => '至少需要1个提示词',
                'prompts.max' => '最多支持10个提示词',
                'prompts.*.required' => '提示词不能为空',
                'prompts.*.min' => '提示词至少5个字符',
                'prompts.*.max' => '提示词不能超过2000个字符',
                'project_id.exists' => '项目不存在'
            ];

            try {
                $this->validateData($request->all(), $rules, $messages, []);
            } catch (\Illuminate\Validation\ValidationException $e) {
                return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
            }

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->imageService->batchGenerateImages(
                $user->id,
                $request->prompts,
                $request->project_id,
                $request->get('common_params', [])
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('批量图像生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                $this->sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量图像生成失败');
        }
    }
}
