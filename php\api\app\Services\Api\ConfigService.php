<?php

namespace App\Services\Api;

use App\Services\Service;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

/**
 * 配置服务类
 * 负责系统配置的管理、读取和更新
 */
class ConfigService extends Service
{
    private $configCacheKey = 'system_config_cache';
    private $configCacheTtl = 3600; // 1小时
    
    /**
     * 获取配置项
     */
    public function get($key, $default = null)
    {
        try {
            $value = Config::get($key, $default);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置获取成功',
                'data' => [
                    'key' => $key,
                    'value' => $value,
                    'exists' => Config::has($key)
                ]
            ];
        } catch (\Exception $e) {
            Log::error("配置获取失败", ['key' => $key, 'error' => $e->getMessage()]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 设置配置项（运行时）
     */
    public function set($key, $value)
    {
        try {
            Config::set($key, $value);
            
            // 清除配置缓存
            Cache::forget($this->configCacheKey);
            
            Log::info("配置设置成功", ['key' => $key]);
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置设置成功（运行时有效）',
                'data' => [
                    'key' => $key,
                    'value' => $value,
                    'set_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            Log::error("配置设置失败", ['key' => $key, 'error' => $e->getMessage()]);
            
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置设置失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取所有配置
     */
    public function getAll($section = null)
    {
        try {
            if ($section) {
                $config = Config::get($section, []);
            } else {
                $config = Config::all();
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置获取成功',
                'data' => [
                    'section' => $section,
                    'config' => $config,
                    'count' => is_array($config) ? count($config) : 1
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取应用配置信息
     */
    public function getAppConfig()
    {
        try {
            $config = [
                'app' => [
                    'name' => Config::get('app.name'),
                    'env' => Config::get('app.env'),
                    'debug' => Config::get('app.debug'),
                    'url' => Config::get('app.url'),
                    'timezone' => Config::get('app.timezone'),
                    'locale' => Config::get('app.locale'),
                    'version' => Config::get('app.version', '1.0.0')
                ],
                'database' => [
                    'default' => Config::get('database.default'),
                    'connections' => array_keys(Config::get('database.connections', []))
                ],
                'cache' => [
                    'default' => Config::get('cache.default'),
                    'stores' => array_keys(Config::get('cache.stores', []))
                ],
                'queue' => [
                    'default' => Config::get('queue.default'),
                    'connections' => array_keys(Config::get('queue.connections', []))
                ],
                'mail' => [
                    'default' => Config::get('mail.default'),
                    'mailers' => array_keys(Config::get('mail.mailers', []))
                ]
            ];
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '应用配置获取成功',
                'data' => $config
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '应用配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取数据库配置
     */
    public function getDatabaseConfig()
    {
        try {
            $connections = Config::get('database.connections', []);
            $safeConnections = [];
            
            foreach ($connections as $name => $config) {
                $safeConnections[$name] = [
                    'driver' => $config['driver'] ?? 'unknown',
                    'host' => $config['host'] ?? 'unknown',
                    'port' => $config['port'] ?? 'unknown',
                    'database' => $config['database'] ?? 'unknown',
                    'username' => isset($config['username']) ? '***' : null,
                    'password' => isset($config['password']) ? '***' : null,
                    'charset' => $config['charset'] ?? 'unknown',
                    'collation' => $config['collation'] ?? 'unknown'
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '数据库配置获取成功',
                'data' => [
                    'default' => Config::get('database.default'),
                    'connections' => $safeConnections
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '数据库配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取缓存配置
     */
    public function getCacheConfig()
    {
        try {
            $stores = Config::get('cache.stores', []);
            $safeStores = [];
            
            foreach ($stores as $name => $config) {
                $safeStores[$name] = [
                    'driver' => $config['driver'] ?? 'unknown',
                    'host' => $config['host'] ?? null,
                    'port' => $config['port'] ?? null,
                    'database' => $config['database'] ?? null,
                    'password' => isset($config['password']) ? '***' : null,
                    'prefix' => $config['prefix'] ?? null
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '缓存配置获取成功',
                'data' => [
                    'default' => Config::get('cache.default'),
                    'prefix' => Config::get('cache.prefix'),
                    'stores' => $safeStores
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '缓存配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取队列配置
     */
    public function getQueueConfig()
    {
        try {
            $connections = Config::get('queue.connections', []);
            $safeConnections = [];
            
            foreach ($connections as $name => $config) {
                $safeConnections[$name] = [
                    'driver' => $config['driver'] ?? 'unknown',
                    'host' => $config['host'] ?? null,
                    'port' => $config['port'] ?? null,
                    'queue' => $config['queue'] ?? null,
                    'retry_after' => $config['retry_after'] ?? null,
                    'block_for' => $config['block_for'] ?? null
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '队列配置获取成功',
                'data' => [
                    'default' => Config::get('queue.default'),
                    'connections' => $safeConnections,
                    'failed' => [
                        'driver' => Config::get('queue.failed.driver'),
                        'database' => Config::get('queue.failed.database'),
                        'table' => Config::get('queue.failed.table')
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '队列配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取日志配置
     */
    public function getLogConfig()
    {
        try {
            $channels = Config::get('logging.channels', []);
            $safeChannels = [];
            
            foreach ($channels as $name => $config) {
                $safeChannels[$name] = [
                    'driver' => $config['driver'] ?? 'unknown',
                    'level' => $config['level'] ?? 'debug',
                    'path' => $config['path'] ?? null,
                    'days' => $config['days'] ?? null,
                    'max_files' => $config['max_files'] ?? null
                ];
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '日志配置获取成功',
                'data' => [
                    'default' => Config::get('logging.default'),
                    'deprecations' => Config::get('logging.deprecations'),
                    'channels' => $safeChannels
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '日志配置获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 验证配置
     */
    public function validateConfig($section = null)
    {
        try {
            $validationResults = [];
            
            if (!$section || $section === 'app') {
                $validationResults['app'] = $this->validateAppConfig();
            }
            
            if (!$section || $section === 'database') {
                $validationResults['database'] = $this->validateDatabaseConfig();
            }
            
            if (!$section || $section === 'cache') {
                $validationResults['cache'] = $this->validateCacheConfig();
            }
            
            if (!$section || $section === 'queue') {
                $validationResults['queue'] = $this->validateQueueConfig();
            }
            
            $overallValid = collect($validationResults)->every(function ($result) {
                return $result['valid'];
            });
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => $overallValid ? '配置验证通过' : '配置验证发现问题',
                'data' => [
                    'overall_valid' => $overallValid,
                    'section' => $section,
                    'results' => $validationResults,
                    'validated_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置验证失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取环境变量
     */
    public function getEnvVars($pattern = null)
    {
        try {
            $envVars = $_ENV;
            
            if ($pattern) {
                $envVars = array_filter($envVars, function ($key) use ($pattern) {
                    return strpos($key, $pattern) !== false;
                }, ARRAY_FILTER_USE_KEY);
            }
            
            // 隐藏敏感信息
            $sensitiveKeys = ['PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'PRIVATE'];
            foreach ($envVars as $key => $value) {
                foreach ($sensitiveKeys as $sensitive) {
                    if (strpos(strtoupper($key), $sensitive) !== false) {
                        $envVars[$key] = '***';
                        break;
                    }
                }
            }
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '环境变量获取成功',
                'data' => [
                    'pattern' => $pattern,
                    'count' => count($envVars),
                    'variables' => $envVars
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '环境变量获取失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 清除配置缓存
     */
    public function clearCache()
    {
        try {
            // 清除Laravel配置缓存
            if (File::exists(base_path('bootstrap/cache/config.php'))) {
                File::delete(base_path('bootstrap/cache/config.php'));
            }
            
            // 清除自定义配置缓存
            Cache::forget($this->configCacheKey);
            
            Log::info("配置缓存清除成功");
            
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置缓存清除成功',
                'data' => [
                    'cleared_at' => now()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置缓存清除失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 验证应用配置
     */
    private function validateAppConfig()
    {
        $errors = [];
        
        if (!Config::get('app.name')) {
            $errors[] = 'APP_NAME 未设置';
        }
        
        if (!Config::get('app.key')) {
            $errors[] = 'APP_KEY 未设置';
        }
        
        if (!in_array(Config::get('app.env'), ['local', 'development', 'staging', 'production'])) {
            $errors[] = 'APP_ENV 值无效';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 验证数据库配置
     */
    private function validateDatabaseConfig()
    {
        $errors = [];
        
        $defaultConnection = Config::get('database.default');
        $connections = Config::get('database.connections', []);
        
        if (!$defaultConnection) {
            $errors[] = '默认数据库连接未设置';
        }
        
        if (!isset($connections[$defaultConnection])) {
            $errors[] = "默认数据库连接 '{$defaultConnection}' 配置不存在";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 验证缓存配置
     */
    private function validateCacheConfig()
    {
        $errors = [];
        
        $defaultStore = Config::get('cache.default');
        $stores = Config::get('cache.stores', []);
        
        if (!$defaultStore) {
            $errors[] = '默认缓存存储未设置';
        }
        
        if (!isset($stores[$defaultStore])) {
            $errors[] = "默认缓存存储 '{$defaultStore}' 配置不存在";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 验证队列配置
     */
    private function validateQueueConfig()
    {
        $errors = [];
        
        $defaultConnection = Config::get('queue.default');
        $connections = Config::get('queue.connections', []);
        
        if (!$defaultConnection) {
            $errors[] = '默认队列连接未设置';
        }
        
        if (!isset($connections[$defaultConnection])) {
            $errors[] = "默认队列连接 '{$defaultConnection}' 配置不存在";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 获取系统配置列表
     */
    public function getConfigs($filters = [])
    {
        try {
            // 模拟配置数据
            $allConfigs = [
                [
                    'id' => 1,
                    'key' => 'ai.default_platform',
                    'value' => 'liblib',
                    'category' => 'ai',
                    'description' => '默认AI平台',
                    'type' => 'string',
                    'options' => ['liblib', 'deepseek', 'kling', 'minimax'],
                    'updated_at' => '2024-01-01 12:00:00'
                ],
                [
                    'id' => 2,
                    'key' => 'system.max_file_size',
                    'value' => 10485760,
                    'category' => 'system',
                    'description' => '最大文件上传大小（字节）',
                    'type' => 'integer',
                    'options' => null,
                    'updated_at' => '2024-01-01 10:00:00'
                ],
                [
                    'id' => 3,
                    'key' => 'payment.default_currency',
                    'value' => 'CNY',
                    'category' => 'payment',
                    'description' => '默认货币',
                    'type' => 'string',
                    'options' => ['CNY', 'USD', 'EUR'],
                    'updated_at' => '2024-01-01 09:00:00'
                ],
                [
                    'id' => 4,
                    'key' => 'feature.image_generation',
                    'value' => true,
                    'category' => 'feature',
                    'description' => '图像生成功能开关',
                    'type' => 'boolean',
                    'options' => null,
                    'updated_at' => '2024-01-01 08:00:00'
                ],
                [
                    'id' => 5,
                    'key' => 'security.session_timeout',
                    'value' => 7200,
                    'category' => 'security',
                    'description' => '会话超时时间（秒）',
                    'type' => 'integer',
                    'options' => null,
                    'updated_at' => '2024-01-01 07:00:00'
                ]
            ];

            // 应用过滤器
            $configs = $allConfigs;
            
            if (!empty($filters['category'])) {
                $configs = array_filter($configs, function($config) use ($filters) {
                    return $config['category'] === $filters['category'];
                });
            }
            
            if (!empty($filters['key'])) {
                $configs = array_filter($configs, function($config) use ($filters) {
                    return strpos($config['key'], $filters['key']) !== false;
                });
            }

            // 重新索引数组
            $configs = array_values($configs);
            
            // 分页处理
            $page = $filters['page'] ?? 1;
            $perPage = $filters['per_page'] ?? 50;
            $total = count($configs);
            $lastPage = ceil($total / $perPage);
            $offset = ($page - 1) * $perPage;
            $configs = array_slice($configs, $offset, $perPage);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'configs' => $configs,
                    'categories' => ['system', 'ai', 'payment', 'feature', 'security'],
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("获取配置列表失败", ['error' => $e->getMessage()]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取配置列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取公开配置
     */
    public function getPublicConfig()
    {
        try {
            $publicConfig = [
                'app_name' => 'TipTop AI创作平台',
                'app_version' => '1.0.0',
                'features' => [
                    'image_generation' => true,
                    'video_generation' => true,
                    'voice_synthesis' => true,
                    'music_generation' => false
                ],
                'limits' => [
                    'max_file_size' => 10485760,
                    'max_generation_time' => 300,
                    'daily_free_quota' => 10
                ],
                'ai_platforms' => [
                    'available' => ['liblib', 'deepseek', 'kling', 'minimax'],
                    'default' => 'liblib'
                ]
            ];

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $publicConfig
            ]);
        } catch (\Exception $e) {
            Log::error("获取公开配置失败", ['error' => $e->getMessage()]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取公开配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新配置
     */
    public function updateConfig($id, $value, $userId)
    {
        try {
            // 模拟配置更新
            $oldValue = 'liblib'; // 模拟旧值
            
            Log::info("配置更新成功", [
                'config_id' => $id,
                'old_value' => $oldValue,
                'new_value' => $value,
                'user_id' => $userId
            ]);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置更新成功',
                'data' => [
                    'id' => $id,
                    'key' => 'ai.default_platform',
                    'value' => $value,
                    'old_value' => $oldValue,
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("配置更新失败", [
                'config_id' => $id,
                'value' => $value,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置更新失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 批量更新配置
     */
    public function batchUpdateConfigs($configs, $userId)
    {
        try {
            $updatedConfigs = [];
            $updatedCount = 0;
            $failedCount = 0;

            foreach ($configs as $config) {
                try {
                    // 模拟更新单个配置
                    $updatedConfigs[] = [
                        'id' => $config['id'],
                        'key' => 'ai.default_platform', // 模拟键名
                        'value' => $config['value']
                    ];
                    $updatedCount++;
                } catch (\Exception $e) {
                    $failedCount++;
                    Log::error("批量更新配置项失败", [
                        'config_id' => $config['id'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("批量配置更新完成", [
                'updated_count' => $updatedCount,
                'failed_count' => $failedCount,
                'user_id' => $userId
            ]);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置批量更新成功',
                'data' => [
                    'updated_count' => $updatedCount,
                    'failed_count' => $failedCount,
                    'updated_configs' => $updatedConfigs
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("批量配置更新失败", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '批量配置更新失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 重置配置为默认值
     */
    public function resetConfig($id, $userId)
    {
        try {
            $oldValue = 'deepseek'; // 模拟当前值
            $defaultValue = 'liblib'; // 模拟默认值
            
            Log::info("配置重置成功", [
                'config_id' => $id,
                'old_value' => $oldValue,
                'default_value' => $defaultValue,
                'user_id' => $userId
            ]);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '配置重置成功',
                'data' => [
                    'id' => $id,
                    'key' => 'ai.default_platform',
                    'value' => $defaultValue,
                    'old_value' => $oldValue,
                    'reset_at' => now()->format('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("配置重置失败", [
                'config_id' => $id,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置重置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取配置历史
     */
    public function getConfigHistory($id, $page = 1)
    {
        try {
            // 模拟配置历史数据
            $history = [
                [
                    'id' => 1,
                    'old_value' => 'liblib',
                    'new_value' => 'deepseek',
                    'operator' => 'admin',
                    'operator_id' => 1,
                    'created_at' => '2024-01-01 12:00:00',
                    'reason' => '切换默认AI平台'
                ],
                [
                    'id' => 2,
                    'old_value' => 'kling',
                    'new_value' => 'liblib',
                    'operator' => 'admin',
                    'operator_id' => 1,
                    'created_at' => '2024-01-01 10:00:00',
                    'reason' => '恢复默认设置'
                ]
            ];

            $perPage = 20;
            $total = count($history);
            $lastPage = ceil($total / $perPage);
            $offset = ($page - 1) * $perPage;
            $paginatedHistory = array_slice($history, $offset, $perPage);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'config' => [
                        'id' => $id,
                        'key' => 'ai.default_platform',
                        'current_value' => 'deepseek'
                    ],
                    'history' => $paginatedHistory,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $perPage,
                        'total' => $total,
                        'last_page' => $lastPage
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("获取配置历史失败", [
                'config_id' => $id,
                'page' => $page,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取配置历史失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 验证配置值
     */
    public function validateConfigValue($key, $value)
    {
        try {
            $valid = true;
            $formattedValue = $value;
            $warnings = [];

            // 根据配置键进行验证
            switch ($key) {
                case 'ai.default_platform':
                    $allowedPlatforms = ['liblib', 'deepseek', 'kling', 'minimax'];
                    if (!in_array($value, $allowedPlatforms)) {
                        $valid = false;
                        $warnings[] = '不支持的AI平台，允许的值：' . implode(', ', $allowedPlatforms);
                    }
                    break;
                    
                case 'system.max_file_size':
                    if (!is_numeric($value) || $value <= 0) {
                        $valid = false;
                        $warnings[] = '文件大小必须是正整数';
                    } elseif ($value > 104857600) { // 100MB
                        $warnings[] = '文件大小超过推荐值100MB，可能影响性能';
                    }
                    $formattedValue = (int)$value;
                    break;
                    
                case 'feature.image_generation':
                    if (!is_bool($value) && !in_array($value, ['true', 'false', '1', '0'])) {
                        $valid = false;
                        $warnings[] = '功能开关必须是布尔值';
                    }
                    $formattedValue = (bool)$value;
                    break;
                    
                default:
                    // 通用验证
                    if (is_string($value) && strlen($value) > 1000) {
                        $warnings[] = '配置值过长，建议不超过1000个字符';
                    }
                    break;
            }

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => $valid ? '配置值验证通过' : '配置值验证失败',
                'data' => [
                    'valid' => $valid,
                    'formatted_value' => $formattedValue,
                    'warnings' => $warnings
                ]
            ]);
        } catch (\Exception $e) {
            Log::error("配置值验证失败", [
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '配置值验证失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}